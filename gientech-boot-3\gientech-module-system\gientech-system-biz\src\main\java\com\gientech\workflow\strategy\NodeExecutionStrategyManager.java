package com.gientech.workflow.strategy;

import com.gientech.workflow.define.WorkflowDefine;
import com.gientech.workflow.define.WorkflowNode;
import com.gientech.workflow.entity.WorkflowInstance;
import com.gientech.workflow.entity.WorkflowTask;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 节点执行策略管理器
 * 负责管理所有的节点执行策略，并根据节点类型选择合适的策略
 *
 * <AUTHOR>
 * @date 2025年08月07日
 */
@Slf4j
@Component
public class NodeExecutionStrategyManager {

    @Autowired
    private List<NodeExecutionStrategy> executionStrategies;

    @PostConstruct
    public void init() {
        log.info("初始化节点执行策略管理器，共注册 {} 个策略", executionStrategies.size());
        for (NodeExecutionStrategy strategy : executionStrategies) {
            log.info("注册执行策略: {}", strategy.getClass().getSimpleName());
        }
    }

    /**
     * 执行节点
     *
     * @param instance 工作流实例
     * @param node 当前节点
     * @param define 工作流定义
     * @param variables 变量参数
     * @param task 当前任务（可能为null）
     * @return 执行结果
     */
    public NodeExecutionResult executeNode(WorkflowInstance instance, WorkflowNode node,
                                           WorkflowDefine define, Map<String, Object> variables,
                                           WorkflowTask task) {
        for (NodeExecutionStrategy strategy : executionStrategies) {
            if (strategy.supports(node)) {
                log.debug("使用策略 {} 执行节点，节点类型: {}",
                        strategy.getClass().getSimpleName(), node.getType());
                return strategy.execute(instance, node, define, variables, task);
            }
        }

        log.error("未找到支持节点类型 {} 的执行策略", node.getType());
        return NodeExecutionResult.failure("未找到支持节点类型 " + node.getType() + " 的执行策略");
    }

    /**
     * 检查是否有策略支持该节点类型
     *
     * @param node 工作流节点
     * @return 是否支持
     */
    public boolean isSupported(WorkflowNode node) {
        return executionStrategies.stream().anyMatch(strategy -> strategy.supports(node));
    }
}
