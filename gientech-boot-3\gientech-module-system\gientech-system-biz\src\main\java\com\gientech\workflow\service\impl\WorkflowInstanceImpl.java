package com.gientech.workflow.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gientech.workflow.cache.WorkflowDefineCache;
import com.gientech.workflow.define.WorkflowDefine;
import com.gientech.workflow.define.WorkflowNode;
import com.gientech.workflow.entity.InstanceStatus;
import com.gientech.workflow.entity.WorkflowInstance;
import com.gientech.workflow.mapper.WorkflowInstanceMapper;
import com.gientech.workflow.mapper.WorkflowTaskMapper;
import com.gientech.workflow.parser.NodeParseUtil;
import com.gientech.workflow.process.WorkflowProcessRecorder;
import com.gientech.workflow.service.IWorkflowInstanceService;
import com.gientech.workflow.strategy.NodeExecutionResult;
import com.gientech.workflow.strategy.NodeExecutionStrategyManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

/**
 * 工作流实例 服务实现
 *
 * <AUTHOR>
 * @date 2025年05月08日 13:39
 */
@Slf4j
@Service
public class WorkflowInstanceImpl extends ServiceImpl<WorkflowInstanceMapper, WorkflowInstance> implements IWorkflowInstanceService {

    /**
     * 找到下一个节点
     */
    private NodeParseUtil nodeParseUtil;

    /**
     * 流程定义缓存
     */
    private WorkflowDefineCache workflowDefineCache;

    /**
     * 工作流任务 持久层接口
     */
    private WorkflowTaskMapper workflowTaskMapper;

    /**
     * 节点执行策略管理器
     */
    private NodeExecutionStrategyManager nodeExecutionStrategyManager;

    @Autowired
    public void setNodeParseUtil(NodeParseUtil nodeParseUtil) {
        this.nodeParseUtil = nodeParseUtil;
    }

    @Autowired
    public void setWorkflowDefineCache(WorkflowDefineCache workflowDefineCache) {
        this.workflowDefineCache = workflowDefineCache;
    }

    @Autowired
    public void setWorkflowTaskMapper(WorkflowTaskMapper workflowTaskMapper) {
        this.workflowTaskMapper = workflowTaskMapper;
    }

    @Autowired
    public void setNodeExecutionStrategyManager(NodeExecutionStrategyManager nodeExecutionStrategyManager) {
        this.nodeExecutionStrategyManager = nodeExecutionStrategyManager;
    }

    @Autowired
    private WorkflowProcessRecorder processRecorder;

    /**
     * 创建工作流实例
     *
     * @param businessKey 业务key
     * @param businessId 业务主键
     * @param variables 参数
     * @return 工作流实例
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public WorkflowInstance createWorkflowInstance(String businessKey, String businessId, Map<String, Object> variables) {
        WorkflowDefine define = workflowDefineCache.get(businessKey);
        String startNodeId = define.getStartNodeId();

        // 创建工作流实例
        WorkflowInstance instance = WorkflowInstance.builder()
                .businessId(businessId)
                .workflowDefineId(define.getId())
                .status(InstanceStatus.started)
                .currentNodeId(startNodeId)
                .variables(variables)
                .build();
        baseMapper.insert(instance);

        // 记录实例创建过程
        processRecorder.recordInstanceCreation(instance, define, variables);

        // 获取下一个节点
        WorkflowNode nextNode = nodeParseUtil.getNextNode(define, startNodeId, variables);
        if (nextNode == null) {
            log.error("创建实例时,获取到的下一个节点为空");
            log.error("当前实例:{}", instance.toString());
            log.error("当前定义:{}", define.toString());
            throw new RuntimeException("Next node is null");
        }

        // 更新实例当前节点
        instance.setCurrentNodeId(nextNode.getId());
        baseMapper.updateById(instance);

        // 使用策略模式执行节点
        NodeExecutionResult result = nodeExecutionStrategyManager.executeNode(instance, nextNode, define, variables, null);

        if (!result.isSuccess()) {
            log.error("执行节点失败: {}", result.getErrorMessage());
            throw new RuntimeException("执行节点失败: " + result.getErrorMessage());
        }

        // 处理执行结果
        if (result.getInstanceStatus() != null) {
            instance.setStatus(result.getInstanceStatus());
        }

        if (result.getNewTask() != null) {
            workflowTaskMapper.insert(result.getNewTask());
        }

        baseMapper.updateById(instance);
        log.info("工作流实例创建成功: {}", result.getDescription());

        return instance;
    }
}
