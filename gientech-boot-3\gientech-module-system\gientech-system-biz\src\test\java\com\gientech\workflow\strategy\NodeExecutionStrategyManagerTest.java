package com.gientech.workflow.strategy;

import com.gientech.workflow.define.NodeType;
import com.gientech.workflow.define.WorkflowDefine;
import com.gientech.workflow.define.WorkflowNode;
import com.gientech.workflow.entity.InstanceStatus;
import com.gientech.workflow.entity.WorkflowInstance;
import com.gientech.workflow.entity.WorkflowTask;
import com.gientech.workflow.strategy.impl.EndNodeExecutionStrategy;
import com.gientech.workflow.strategy.impl.UserTaskExecutionStrategy;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * 节点执行策略管理器测试
 *
 * <AUTHOR>
 * @date 2025年08月07日
 */
@ExtendWith(MockitoExtension.class)
class NodeExecutionStrategyManagerTest {

    @Mock
    private UserTaskExecutionStrategy userTaskStrategy;

    @Mock
    private EndNodeExecutionStrategy endNodeStrategy;

    @InjectMocks
    private NodeExecutionStrategyManager strategyManager;

    private WorkflowInstance testInstance;
    private WorkflowDefine testDefine;
    private WorkflowNode userTaskNode;
    private WorkflowNode endNode;
    private Map<String, Object> variables;

    @BeforeEach
    void setUp() {
        // 通过反射设置策略列表
        try {
            java.lang.reflect.Field field = NodeExecutionStrategyManager.class.getDeclaredField("executionStrategies");
            field.setAccessible(true);
            field.set(strategyManager, Arrays.asList(userTaskStrategy, endNodeStrategy));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        // 创建测试数据
        testInstance = WorkflowInstance.builder()
                .id("instance-1")
                .businessId("business-1")
                .status(InstanceStatus.processing)
                .build();

        testDefine = new WorkflowDefine();
        testDefine.setId("define-1");
        testDefine.setBusinessKey("testBusiness");

        userTaskNode = new WorkflowNode();
        userTaskNode.setId("user-task-1");
        userTaskNode.setName("用户审核");
        userTaskNode.setType(NodeType.userTask);

        endNode = new WorkflowNode();
        endNode.setId("end-1");
        endNode.setName("结束");
        endNode.setType(NodeType.end);

        variables = new HashMap<>();
        variables.put("businessKey", "testBusiness");
    }

    @Test
    void testExecuteUserTaskNode() {
        // 准备测试数据
        NodeExecutionResult expectedResult = NodeExecutionResult.success(
                "用户任务创建成功",
                InstanceStatus.processing
        );

        // 设置mock行为
        when(userTaskStrategy.supports(userTaskNode)).thenReturn(true);
        when(userTaskStrategy.execute(testInstance, userTaskNode, testDefine, variables, null))
                .thenReturn(expectedResult);
        when(endNodeStrategy.supports(userTaskNode)).thenReturn(false);

        // 执行测试
        NodeExecutionResult result = strategyManager.executeNode(
                testInstance, userTaskNode, testDefine, variables, null);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals("用户任务创建成功", result.getDescription());
        assertEquals(InstanceStatus.processing, result.getInstanceStatus());
    }

    @Test
    void testExecuteEndNode() {
        // 准备测试数据
        NodeExecutionResult expectedResult = NodeExecutionResult.success(
                "工作流实例完成",
                InstanceStatus.completed
        );

        // 设置mock行为
        when(userTaskStrategy.supports(endNode)).thenReturn(false);
        when(endNodeStrategy.supports(endNode)).thenReturn(true);
        when(endNodeStrategy.execute(testInstance, endNode, testDefine, variables, null))
                .thenReturn(expectedResult);

        // 执行测试
        NodeExecutionResult result = strategyManager.executeNode(
                testInstance, endNode, testDefine, variables, null);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals("工作流实例完成", result.getDescription());
        assertEquals(InstanceStatus.completed, result.getInstanceStatus());
    }

    @Test
    void testUnsupportedNodeType() {
        WorkflowNode unsupportedNode = new WorkflowNode();
        unsupportedNode.setId("unknown-1");
        unsupportedNode.setType(NodeType.gateway);

        // 设置mock行为
        when(userTaskStrategy.supports(unsupportedNode)).thenReturn(false);
        when(endNodeStrategy.supports(unsupportedNode)).thenReturn(false);

        // 执行测试
        NodeExecutionResult result = strategyManager.executeNode(
                testInstance, unsupportedNode, testDefine, variables, null);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertTrue(result.getErrorMessage().contains("未找到支持节点类型"));
    }

    @Test
    void testIsSupported() {
        // 设置mock行为
        when(userTaskStrategy.supports(userTaskNode)).thenReturn(true);

        // 执行测试
        boolean result = strategyManager.isSupported(userTaskNode);

        // 验证结果
        assertTrue(result);
    }

    @Test
    void testIsNotSupported() {
        WorkflowNode unsupportedNode = new WorkflowNode();
        unsupportedNode.setType(NodeType.gateway);

        // 设置mock行为
        when(userTaskStrategy.supports(unsupportedNode)).thenReturn(false);
        when(endNodeStrategy.supports(unsupportedNode)).thenReturn(false);

        // 执行测试
        boolean result = strategyManager.isSupported(unsupportedNode);

        // 验证结果
        assertFalse(result);
    }
}
