package com.gientech.workflow.factory.impl;

import com.gientech.workflow.define.NodeType;
import com.gientech.workflow.define.WorkflowNode;
import com.gientech.workflow.entity.WorkflowInstance;
import com.gientech.workflow.entity.WorkflowTask;
import com.gientech.workflow.factory.WorkflowTaskFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 开始节点任务工厂
 * 开始节点不需要创建任务
 *
 * <AUTHOR>
 * @date 2025年08月07日
 */
@Slf4j
@Component
public class StartTaskFactory implements WorkflowTaskFactory {

    @Override
    public WorkflowTask createTask(WorkflowInstance instance, WorkflowNode node, Map<String, Object> variables) {
        if (!supports(node)) {
            return null;
        }

        log.info("处理开始节点: 节点ID={}, 节点名称={}", node.getId(), node.getName());

        // 开始节点不需要创建任务，返回null
        return null;
    }

    @Override
    public boolean supports(WorkflowNode node) {
        return NodeType.start.equals(node.getType());
    }
}
