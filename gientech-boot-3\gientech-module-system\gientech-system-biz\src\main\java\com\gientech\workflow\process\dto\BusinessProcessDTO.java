package com.gientech.workflow.process.dto;

import com.gientech.workflow.process.enums.ProcessResult;
import com.gientech.workflow.process.enums.ProcessType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Map;

/**
 * 业务处理过程数据传输对象
 *
 * <AUTHOR>
 * @date 2025年08月07日
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "业务处理过程数据传输对象")
public class BusinessProcessDTO {

    /**
     * 业务标识
     */
    @Schema(description = "业务标识，用于区分不同的业务模块", required = true)
    private String businessKey;

    /**
     * 业务主键
     */
    @Schema(description = "业务数据的主键ID", required = true)
    private String businessId;

    /**
     * 处理过程类型
     */
    @Schema(description = "处理过程类型", required = true)
    private ProcessType processType;

    /**
     * 操作类型
     */
    @Schema(description = "具体的操作类型，如：创建、提交、审核通过、反馈等", required = true)
    private String actionType;

    /**
     * 过程描述
     */
    @Schema(description = "处理过程的详细描述", required = true)
    private String processDescription;

    /**
     * 操作结果
     */
    @Schema(description = "操作结果")
    private ProcessResult processResult;

    /**
     * 工作流实例ID
     */
    @Schema(description = "关联的工作流实例ID，非工作流操作时为空")
    private String workflowInstanceId;

    /**
     * 工作流任务ID
     */
    @Schema(description = "关联的工作流任务ID，非工作流操作时为空")
    private String workflowTaskId;

    /**
     * 节点名称
     */
    @Schema(description = "工作流节点名称，非工作流操作时为空")
    private String nodeName;

    /**
     * 操作备注
     */
    @Schema(description = "操作时的备注信息，如审核意见等")
    private String operationRemark;

    /**
     * 扩展数据
     */
    @Schema(description = "扩展数据，存储额外的业务信息")
    private Map<String, Object> extendData;

    /**
     * 操作人ID
     */
    @Schema(description = "执行操作的用户ID")
    private String operatorId;

    /**
     * 操作人姓名
     */
    @Schema(description = "执行操作的用户姓名")
    private String operatorName;

    /**
     * 操作机构代码
     */
    @Schema(description = "执行操作的机构代码")
    private String operatorOrgCode;

    /**
     * 操作机构名称
     */
    @Schema(description = "执行操作的机构名称")
    private String operatorOrgName;

    /**
     * 操作时间
     */
    @Schema(description = "操作执行时间")
    private Date operationTime;

    /**
     * 创建业务操作的处理过程记录
     *
     * @param businessKey 业务标识
     * @param businessId 业务主键
     * @param actionType 操作类型
     * @param description 过程描述
     * @return 处理过程DTO
     */
    public static BusinessProcessDTO createBusinessProcess(String businessKey, String businessId,
                                                           String actionType, String description) {
        return BusinessProcessDTO.builder()
                .businessKey(businessKey)
                .businessId(businessId)
                .processType(ProcessType.BUSINESS)
                .actionType(actionType)
                .processDescription(description)
                .processResult(ProcessResult.SUCCESS)
                .operationTime(new Date())
                .build();
    }

    /**
     * 创建工作流操作的处理过程记录
     *
     * @param businessKey 业务标识
     * @param businessId 业务主键
     * @param actionType 操作类型
     * @param description 过程描述
     * @param workflowInstanceId 工作流实例ID
     * @param workflowTaskId 工作流任务ID
     * @param nodeName 节点名称
     * @return 处理过程DTO
     */
    public static BusinessProcessDTO createWorkflowProcess(String businessKey, String businessId,
                                                           String actionType, String description,
                                                           String workflowInstanceId, String workflowTaskId,
                                                           String nodeName) {
        return BusinessProcessDTO.builder()
                .businessKey(businessKey)
                .businessId(businessId)
                .processType(ProcessType.WORKFLOW)
                .actionType(actionType)
                .processDescription(description)
                .processResult(ProcessResult.SUCCESS)
                .workflowInstanceId(workflowInstanceId)
                .workflowTaskId(workflowTaskId)
                .nodeName(nodeName)
                .operationTime(new Date())
                .build();
    }

    /**
     * 创建系统操作的处理过程记录
     *
     * @param businessKey 业务标识
     * @param businessId 业务主键
     * @param actionType 操作类型
     * @param description 过程描述
     * @return 处理过程DTO
     */
    public static BusinessProcessDTO createSystemProcess(String businessKey, String businessId,
                                                         String actionType, String description) {
        return BusinessProcessDTO.builder()
                .businessKey(businessKey)
                .businessId(businessId)
                .processType(ProcessType.SYSTEM)
                .actionType(actionType)
                .processDescription(description)
                .processResult(ProcessResult.SUCCESS)
                .operationTime(new Date())
                .build();
    }
}
