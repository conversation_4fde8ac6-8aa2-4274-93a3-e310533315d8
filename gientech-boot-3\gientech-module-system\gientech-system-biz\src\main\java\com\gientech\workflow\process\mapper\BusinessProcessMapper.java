package com.gientech.workflow.process.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gientech.workflow.process.entity.BusinessProcess;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 通用业务处理过程记录 Mapper接口
 *
 * <AUTHOR>
 * @date 2025年08月07日
 */
@Mapper
public interface BusinessProcessMapper extends BaseMapper<BusinessProcess> {

    /**
     * 根据业务标识和业务ID查询处理过程列表
     *
     * @param businessKey 业务标识
     * @param businessId 业务ID
     * @return 处理过程列表
     */
    List<BusinessProcess> selectByBusinessKeyAndId(@Param("businessKey") String businessKey,
                                                   @Param("businessId") String businessId);

    /**
     * 根据工作流实例ID查询处理过程列表
     *
     * @param workflowInstanceId 工作流实例ID
     * @return 处理过程列表
     */
    List<BusinessProcess> selectByWorkflowInstanceId(@Param("workflowInstanceId") String workflowInstanceId);

    /**
     * 获取业务的最新处理过程
     *
     * @param businessKey 业务标识
     * @param businessId 业务ID
     * @return 最新的处理过程记录
     */
    BusinessProcess selectLatestByBusinessKeyAndId(@Param("businessKey") String businessKey,
                                                   @Param("businessId") String businessId);

    /**
     * 批量插入处理过程记录
     *
     * @param processList 处理过程列表
     * @return 插入的记录数
     */
    int batchInsert(@Param("list") List<BusinessProcess> processList);

    /**
     * 根据操作人查询处理过程列表
     *
     * @param operatorId 操作人ID
     * @param businessKey 业务标识（可选）
     * @return 处理过程列表
     */
    List<BusinessProcess> selectByOperator(@Param("operatorId") String operatorId,
                                           @Param("businessKey") String businessKey);

    /**
     * 统计业务的处理过程数量
     *
     * @param businessKey 业务标识
     * @param businessId 业务ID
     * @return 处理过程数量
     */
    int countByBusinessKeyAndId(@Param("businessKey") String businessKey,
                                @Param("businessId") String businessId);
}
