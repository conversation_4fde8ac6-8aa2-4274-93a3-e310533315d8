package com.gientech.workflow.process.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * 通用业务处理过程记录
 * 支持工作流内外操作的统一记录和管理
 *
 * <AUTHOR>
 * @date 2025年08月07日
 */
@Data
@TableName(value = "business_process", autoResultMap = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description = "通用业务处理过程记录")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BusinessProcess implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private String id;

    /**
     * 业务标识
     */
    @Excel(name = "业务标识", width = 15)
    @Schema(description = "业务标识，用于区分不同的业务模块")
    private String businessKey;

    /**
     * 业务主键
     */
    @Excel(name = "业务主键", width = 15)
    @Schema(description = "业务数据的主键ID")
    private String businessId;

    /**
     * 处理过程类型
     */
    @Excel(name = "处理过程类型", width = 15, dicCode = "process_type")
    @Schema(description = "处理过程类型：WORKFLOW-工作流操作，BUSINESS-业务操作")
    @Dict(dicCode = "process_type")
    private String processType;

    /**
     * 操作类型
     */
    @Excel(name = "操作类型", width = 15)
    @Schema(description = "具体的操作类型，如：创建、提交、审核通过、反馈等")
    private String actionType;

    /**
     * 过程描述
     */
    @Excel(name = "过程描述", width = 30)
    @Schema(description = "处理过程的详细描述")
    private String processDescription;

    /**
     * 操作结果
     */
    @Excel(name = "操作结果", width = 15, dicCode = "process_result")
    @Schema(description = "操作结果：SUCCESS-成功，FAILED-失败，PENDING-待处理")
    @Dict(dicCode = "process_result")
    private String processResult;

    /**
     * 工作流实例ID
     */
    @Excel(name = "工作流实例ID", width = 20)
    @Schema(description = "关联的工作流实例ID，非工作流操作时为空")
    private String workflowInstanceId;

    /**
     * 工作流任务ID
     */
    @Excel(name = "工作流任务ID", width = 20)
    @Schema(description = "关联的工作流任务ID，非工作流操作时为空")
    private String workflowTaskId;

    /**
     * 节点名称
     */
    @Excel(name = "节点名称", width = 15)
    @Schema(description = "工作流节点名称，非工作流操作时为空")
    private String nodeName;

    /**
     * 操作备注
     */
    @Excel(name = "操作备注", width = 50)
    @Schema(description = "操作时的备注信息，如审核意见等")
    private String operationRemark;

    /**
     * 扩展数据
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    @Schema(description = "扩展数据，存储额外的业务信息")
    private Map<String, Object> extendData;

    /**
     * 操作人
     */
    @Excel(name = "操作人", width = 15)
    @Schema(description = "执行操作的用户")
    private String operatorId;

    /**
     * 操作人姓名
     */
    @Excel(name = "操作人姓名", width = 15)
    @Schema(description = "执行操作的用户姓名")
    private String operatorName;

    /**
     * 操作机构代码
     */
    @Excel(name = "操作机构", width = 15, dictTable = "sys_depart", dicText = "depart_name", dicCode = "org_code")
    @Schema(description = "执行操作的机构代码")
    @Dict(dictTable = "sys_depart", dicText = "depart_name", dicCode = "org_code")
    private String operatorOrgCode;

    /**
     * 操作机构名称
     */
    @Excel(name = "操作机构名称", width = 20)
    @Schema(description = "执行操作的机构名称")
    private String operatorOrgName;

    /**
     * 操作时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "操作时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "操作执行时间")
    private Date operationTime;

    /**
     * 排序号
     */
    @Schema(description = "处理过程的排序号，用于确定执行顺序")
    private Integer sortOrder;

    /**
     * 创建人
     */
    @Schema(description = "记录创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "记录创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @Schema(description = "记录更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "记录更新时间")
    private Date updateTime;

    /**
     * 所属部门
     */
    @Schema(description = "记录所属部门")
    private String sysOrgCode;
}
