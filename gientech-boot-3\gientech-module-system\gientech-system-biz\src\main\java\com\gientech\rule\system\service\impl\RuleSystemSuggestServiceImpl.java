package com.gientech.rule.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.enums.SqlMethod;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.gientech.rule.manage.reformedAndAbolished.mapper.RuleSystemReformedAndAbolishedMapper;
import com.gientech.rule.regulations.institutionSystemPlan.entity.RuleInstitutionSystemManagement;
import com.gientech.rule.regulations.institutionSystemPlan.mapper.RuleInstitutionSystemManagementMapper;
import com.gientech.rule.system.constant.RuleSystemSuggestConstant;
import com.gientech.rule.system.entity.RuleSystemHistory;
import com.gientech.rule.system.entity.RuleSystemSuggest;
import com.gientech.rule.system.entity.RuleSystemSuggestRelate;
import com.gientech.rule.system.entity.RuleSystemSuggestSmRelate;
import com.gientech.rule.system.mapper.RuleSystemHistoryMapper;
import com.gientech.rule.system.mapper.RuleSystemSuggestMapper;
import com.gientech.rule.system.mapper.RuleSystemSuggestRelateMapper;
import com.gientech.rule.system.mapper.RuleSystemSuggestSmRelateMapper;
import com.gientech.rule.system.service.IRuleSystemSuggestService;
import com.gientech.rule.system.vo.RuleSystemSuggestFeedbackVO;
import com.gientech.rule.system.vo.RuleSystemSuggestVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 内外规模块-制度建议表
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2025-07-16
 */
@Service
public class RuleSystemSuggestServiceImpl extends ServiceImpl<RuleSystemSuggestMapper, RuleSystemSuggest> implements IRuleSystemSuggestService {

    // 制度立改废(制度信息表)
    private RuleSystemReformedAndAbolishedMapper ruleSystemReformedAndAbolishedMapper;

    @Autowired
    public void setRuleSystemReformedAndAbolishedMapper(RuleSystemReformedAndAbolishedMapper rsMapper) {
        this.ruleSystemReformedAndAbolishedMapper = rsMapper;
    }

    // 制度-建议关联表
    private RuleSystemSuggestRelateMapper ruleSystemSuggestRelateMapper;

    @Autowired
    public void setRuleSystemSuggestRelateMapper(RuleSystemSuggestRelateMapper rsMapper) {
        this.ruleSystemSuggestRelateMapper = rsMapper;
    }

    // 制度历史数据表
    private RuleSystemHistoryMapper ruleSystemHistoryMapper;

    @Autowired
    public void setRuleSystemHistoryMapper(RuleSystemHistoryMapper rsMapper) {
        this.ruleSystemHistoryMapper = rsMapper;
    }

    // 制度-规划关联表
    private RuleSystemSuggestSmRelateMapper ruleSystemSuggestSmRelateMapper;

    @Autowired
    public void setRuleSystemSuggestSmRelateMapper(RuleSystemSuggestSmRelateMapper rsMapper) {
        this.ruleSystemSuggestSmRelateMapper = rsMapper;
    }

    // 规划表
    private RuleInstitutionSystemManagementMapper ruleInstitutionSystemManagementMapper;

    @Autowired
    public void setRuleInstitutionSystemManagementMapper(RuleInstitutionSystemManagementMapper rsMapper) {
        this.ruleInstitutionSystemManagementMapper = rsMapper;
    }

    @Override
    public Page<RuleSystemSuggestVO> selectVOPage(Page<RuleSystemSuggestVO> page, @Param(Constants.WRAPPER) Wrapper<RuleSystemSuggestVO> queryWrapper) {
        Page<RuleSystemSuggestVO> ruleSystemSuggestVOPage = ruleSystemReformedAndAbolishedMapper.selectSuggestVOPage(page, queryWrapper);
        ruleSystemSuggestVOPage.getRecords().forEach(ruleSystemSuggestVO -> {
            // 查询建议子表信息
            List<RuleSystemSuggest> ruleSystemSuggests = baseMapper.selectByRelateId(ruleSystemSuggestVO.getId());
            ruleSystemSuggestVO.setRuleSystemSuggestList(ruleSystemSuggests);

            // 查询历史子表信息
            List<RuleSystemHistory> ruleSystemHistories = ruleSystemHistoryMapper.selectByRelateId(ruleSystemSuggestVO.getId());
            ruleSystemSuggestVO.setRuleSystemHistoryList(ruleSystemHistories);
        });
        return ruleSystemSuggestVOPage;
    }

    @Override
    public Page<RuleSystemSuggestFeedbackVO> selectFeedbackVOPage(Page<RuleSystemSuggestFeedbackVO> page, Wrapper<RuleSystemSuggestFeedbackVO> queryWrapper) {
        return baseMapper.selectFeedbackVOPage(page, queryWrapper);
    }

    @Override
    public List<RuleSystemSuggest> selectByRelateId(String relateId) {
        return baseMapper.selectByRelateId(relateId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveVO(RuleSystemSuggestVO ruleSystemSuggestVO) {
        RuleSystemSuggestRelate ruleSystemSuggestRelate = new RuleSystemSuggestRelate();
        ruleSystemSuggestRelate.setSystemId(ruleSystemSuggestVO.getSystemId());
        ruleSystemSuggestRelateMapper.insert(ruleSystemSuggestRelate);
        // 插入子表数据
        List<RuleSystemSuggest> suggestList = ruleSystemSuggestVO.getRuleSystemSuggestList();
        if (suggestList != null && !suggestList.isEmpty()) {
            suggestList.forEach(suggest -> {
                suggest.setRelateId(ruleSystemSuggestRelate.getId());
                suggest.setSystemId(ruleSystemSuggestRelate.getSystemId());
            });
            String sqlStatement = SqlHelper.getSqlStatement(mapperClass, SqlMethod.INSERT_ONE);
            this.executeBatch(suggestList, 100, (sqlSession, entity) -> sqlSession.insert(sqlStatement, entity));
        }
        List<RuleSystemHistory> historyList = ruleSystemSuggestVO.getRuleSystemHistoryList();
        if (historyList != null && !historyList.isEmpty()) {
            historyList.forEach(history -> {
                history.setRelateId(ruleSystemSuggestRelate.getId());
                history.setSystemId(ruleSystemSuggestRelate.getSystemId());
            });
            String sqlStatement = SqlHelper.getSqlStatement(RuleSystemHistoryMapper.class, SqlMethod.INSERT_ONE);
            this.executeBatch(historyList, 100, (sqlSession, entity) -> sqlSession.insert(sqlStatement, entity));
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateVO(RuleSystemSuggestVO ruleSystemSuggestVO) {
        // 删除子表数据
        baseMapper.deleteByRelateId(ruleSystemSuggestVO.getId());
        ruleSystemHistoryMapper.deleteByRelateId(ruleSystemSuggestVO.getId());
        // 子表数据重新插入
        List<RuleSystemSuggest> suggestList = ruleSystemSuggestVO.getRuleSystemSuggestList();
        if (suggestList != null && !suggestList.isEmpty()) {
            suggestList.forEach(suggest -> {
                suggest.setRelateId(ruleSystemSuggestVO.getId());
            });
            String sqlStatement = SqlHelper.getSqlStatement(mapperClass, SqlMethod.INSERT_ONE);
            this.executeBatch(suggestList, 100, (sqlSession, entity) -> sqlSession.insert(sqlStatement, entity));
        }
        List<RuleSystemHistory> historyList = ruleSystemSuggestVO.getRuleSystemHistoryList();
        if (historyList != null && !historyList.isEmpty()) {
            historyList.forEach(history -> {
                history.setRelateId(ruleSystemSuggestVO.getId());
            });
            String sqlStatement = SqlHelper.getSqlStatement(RuleSystemHistoryMapper.class, SqlMethod.INSERT_ONE);
            this.executeBatch(historyList, 100, (sqlSession, entity) -> sqlSession.insert(sqlStatement, entity));
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean feedback(RuleSystemSuggestFeedbackVO ruleSystemSuggestFeedbackVO) {
        // 保存反馈信息
        RuleSystemSuggest ruleSystemSuggest = new RuleSystemSuggest();
        BeanUtils.copyProperties(ruleSystemSuggestFeedbackVO, ruleSystemSuggest);
        if (RuleSystemSuggestConstant.SUGGEST_IS_ADOPT_YES.equals(ruleSystemSuggest.getIsAdopt())) {
            // 建议是否采纳为 是
            // 删除关联数据
            ruleSystemSuggestSmRelateMapper.deleteBySuggestId(ruleSystemSuggestFeedbackVO.getId());
            // 重新生成
            List<RuleInstitutionSystemManagement> ruleSystemManagementList = ruleSystemSuggestFeedbackVO.getRuleSystemManagementList();
            List<RuleSystemSuggestSmRelate> relateList = new ArrayList<>();
            for (RuleInstitutionSystemManagement systemManagement : ruleSystemManagementList) {
                RuleSystemSuggestSmRelate relate = new RuleSystemSuggestSmRelate();
                relate.setSuggestId(ruleSystemSuggestFeedbackVO.getId())
                        .setManagementId(systemManagement.getId())
                        .setRelateTime(new Date());
                relateList.add(relate);
            }
            String sqlStatement = SqlHelper.getSqlStatement(RuleSystemSuggestSmRelateMapper.class, SqlMethod.INSERT_ONE);
            this.executeBatch(relateList, 100, (sqlSession, entity) -> sqlSession.insert(sqlStatement, entity));

            ruleSystemSuggest.setSuggestStatus(RuleSystemSuggestConstant.SUGGEST_STATUS_YES);
        } else if (RuleSystemSuggestConstant.SUGGEST_IS_ADOPT_NO.equals(ruleSystemSuggest.getIsAdopt())) {
            ruleSystemSuggest.setSuggestStatus(RuleSystemSuggestConstant.SUGGEST_STATUS_NO);
        } else {
            // 待定 已关联计划
            ruleSystemSuggest.setSuggestStatus(RuleSystemSuggestConstant.SUGGEST_STATUS_HAS);
        }
        return SqlHelper.retBool(baseMapper.updateById(ruleSystemSuggest));
    }

    public List<RuleInstitutionSystemManagement> selectSystemManagementBySuggestId(String suggestId) {
        List<String> smIdList = ruleSystemSuggestSmRelateMapper.selectSMIdBYSuggestId(suggestId);
        return ruleInstitutionSystemManagementMapper.selectBatchIds(smIdList);
    }
}
