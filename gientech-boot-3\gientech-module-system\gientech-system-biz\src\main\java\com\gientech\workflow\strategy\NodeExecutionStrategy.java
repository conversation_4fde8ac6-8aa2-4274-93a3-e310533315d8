package com.gientech.workflow.strategy;

import com.gientech.workflow.define.WorkflowDefine;
import com.gientech.workflow.define.WorkflowNode;
import com.gientech.workflow.entity.WorkflowInstance;
import com.gientech.workflow.entity.WorkflowTask;

import java.util.Map;

/**
 * 节点执行策略接口
 * 定义不同类型节点的执行逻辑
 *
 * <AUTHOR>
 * @date 2025年08月07日
 */
public interface NodeExecutionStrategy {

    /**
     * 执行节点逻辑
     *
     * @param instance 工作流实例
     * @param node 当前节点
     * @param define 工作流定义
     * @param variables 变量参数
     * @param task 当前任务（可能为null，如开始节点）
     * @return 执行结果
     */
    NodeExecutionResult execute(WorkflowInstance instance, WorkflowNode node,
                                WorkflowDefine define, Map<String, Object> variables,
                                WorkflowTask task);

    /**
     * 判断是否支持该节点类型
     *
     * @param node 工作流节点
     * @return 是否支持
     */
    boolean supports(WorkflowNode node);
}
