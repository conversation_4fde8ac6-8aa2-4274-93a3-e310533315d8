package com.gientech.rule.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 内外规模块-制度建议表
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2025-07-16
 */
@Data
@TableName("rule_system_suggest")
@Accessors(chain = true)
@Schema(description = "内外规模块-制度建议表")
public class RuleSystemSuggest implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private String id;

    /**
     * 关联表外键
     */
    @Excel(name = "关联表外键", width = 15)
    @Schema(description = "关联表外键")
    private String relateId;

    /**
     * 制度表外键
     */
    @Excel(name = "制度表外键", width = 15)
    @Schema(description = "制度表外键")
    private String systemId;

    /**
     * 制度相关条款
     */
    @Excel(name = "制度相关条款", width = 15)
    @Schema(description = "制度相关条款")
    private String relateClause;

    /**
     * 制度建议类型
     */
    @Excel(name = "制度建议类型", width = 15, dicCode = "rule_system_suggest_type")
    @Dict(dicCode = "rule_system_suggest_type")
    @Schema(description = "制度建议类型")
    private String suggestType;

    /**
     * 制度缺陷
     */
    @Excel(name = "制度缺陷", width = 15)
    @Schema(description = "制度缺陷")
    private String systemDefect;

    /**
     * 制度缺陷描述
     */
    @Excel(name = "制度缺陷描述", width = 15)
    @Schema(description = "制度缺陷描述")
    private String defectDescription;

    /**
     * 制度建议描述
     */
    @Excel(name = "制度建议描述", width = 15)
    @Schema(description = "制度建议描述")
    private String suggestDescription;

    /**
     * 建议提出机构
     */
    @Excel(name = "建议提出机构", width = 15, dictTable = "sys_depart", dicText = "depart_name", dicCode = "org_code")
    @Dict(dictTable = "sys_depart", dicText = "depart_name", dicCode = "org_code")
    @Schema(description = "建议提出机构")
    private String proposeInstitution;

    /**
     * 建议提出部门（一级）
     */
    @Excel(name = "建议提出部门（一级）", width = 15, dictTable = "sys_depart", dicText = "depart_name", dicCode = "org_code")
    @Dict(dictTable = "sys_depart", dicText = "depart_name", dicCode = "org_code")
    @Schema(description = "建议提出部门（一级）")
    private String proposeDeptOne;

    /**
     * 建议提出部门（二级）
     */
    @Excel(name = "建议提出部门（二级）", width = 15, dictTable = "sys_depart", dicText = "depart_name", dicCode = "org_code")
    @Dict(dictTable = "sys_depart", dicText = "depart_name", dicCode = "org_code")
    @Schema(description = "建议提出部门（二级）")
    private String proposeDeptTwo;

    /**
     * 建议提出日期
     */
    @Excel(name = "建议提出日期", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "建议提出日期")
    private Date proposeDate;

    /**
     * 建议状态
     */
    @Excel(name = "建议状态", width = 15, dicCode = "rule_system_suggest_status")
    @Dict(dicCode = "rule_system_suggest_status")
    @Schema(description = "建议状态")
    private String suggestStatus;

    /**
     * 流程状态
     */
    @Excel(name = "流程状态", width = 15, dicCode = "rule_assess_pilot_process_status")
    @Dict(dicCode = "rule_assess_pilot_process_status")
    @Schema(description = "流程状态")
    private java.lang.String processStatus;

    /**
     * 建议是否采纳
     */
    @Excel(name = "建议是否采纳", width = 15, dicCode = "rule_system_suggest_is_adopt")
    @Dict(dicCode = "rule_system_suggest_is_adopt")
    @Schema(description = "建议是否采纳")
    private String isAdopt;

    /**
     * 意见反馈部门
     */
    @Excel(name = "意见反馈部门", width = 15, dictTable = "sys_depart", dicText = "depart_name", dicCode = "org_code")
    @Dict(dictTable = "sys_depart", dicText = "depart_name", dicCode = "org_code")
    @Schema(description = "意见反馈部门")
    private String feedbackDept;

    /**
     * 反馈意见
     */
    @Excel(name = "反馈意见", width = 15)
    @Schema(description = "反馈意见")
    private String feedbackOpinion;

    /**
     * 反馈轮次
     */
    @Excel(name = "反馈轮次", width = 15)
    @Schema(description = "反馈轮次")
    private Integer feedbackTurns;

    /**
     * 意见反馈联系人
     */
    @Excel(name = "意见反馈联系人", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Schema(description = "意见反馈联系人")
    private String feedbackPerson;

    /**
     * 意见反馈时间
     */
    @Excel(name = "意见反馈时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "意见反馈时间")
    private Date feedbackTime;

    /**
     * 删除标记
     */
    @Schema(description = "删除标记")
    @TableLogic
    private Integer delFlag;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private Date createTime;

    /**
     * 更新人
     */
    @Schema(description = "更新人")
    private String updateBy;

    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private Date updateTime;

    /**
     * 所属部门
     */
    @Schema(description = "所属部门")
    private String sysOrgCode;

}
