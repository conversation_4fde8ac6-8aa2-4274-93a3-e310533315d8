package com.gientech.rule.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 制度相关历史数据表
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2025-07-16
 */
@Data
@TableName("rule_system_history")
@Accessors(chain = true)
@Schema(description = "制度相关历史数据表")
public class RuleSystemHistory implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private String id;

    /**
     * 关联表外键
     */
    @Excel(name = "关联表外键", width = 15)
    @Schema(description = "关联表外键")
    private String relateId;

    /**
     * 制度表外键
     */
    @Excel(name = "制度表外键", width = 15)
    @Schema(description = "制度表外键")
    private String systemId;

    /**
     * 数据类型
     */
    @Excel(name = "数据类型", width = 15, dicCode = "rule_system_history_type")
    @Dict(dicCode = "rule_system_history_type")
    @Schema(description = "数据类型")
    private String dataType;

    /**
     * 数据主键
     */
    @Excel(name = "数据主键", width = 15)
    @Schema(description = "数据主键")
    private String dataId;

    /**
     * 数据编号
     */
    @Excel(name = "数据编号", width = 15)
    @Schema(description = "数据编号")
    private String dataCode;

    /**
     * 描述信息
     */
    @Excel(name = "描述信息", width = 15)
    @Schema(description = "描述信息")
    private String dataDescription;

    /**
     * 删除标记
     */
    @Schema(description = "删除标记")
    @TableLogic
    private Integer delFlag;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private Date createTime;

    /**
     * 更新人
     */
    @Schema(description = "更新人")
    private String updateBy;

    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private Date updateTime;

    /**
     * 所属部门
     */
    @Schema(description = "所属部门")
    private String sysOrgCode;

}
