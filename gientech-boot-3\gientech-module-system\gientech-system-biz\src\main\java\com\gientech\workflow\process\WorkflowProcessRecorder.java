package com.gientech.workflow.process;

import com.gientech.workflow.define.WorkflowDefine;
import com.gientech.workflow.define.WorkflowNode;
import com.gientech.workflow.entity.WorkflowInstance;
import com.gientech.workflow.entity.WorkflowTask;
import com.gientech.workflow.process.dto.BusinessProcessDTO;
import com.gientech.workflow.process.enums.ProcessResult;
import com.gientech.workflow.process.service.IBusinessProcessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 工作流处理过程记录器
 * 负责记录工作流引擎中的各种操作过程
 *
 * <AUTHOR>
 * @date 2025年08月07日
 */
@Slf4j
@Component
public class WorkflowProcessRecorder {

    @Autowired
    private IBusinessProcessService businessProcessService;

    /**
     * 记录工作流实例创建过程
     *
     * @param instance 工作流实例
     * @param define 工作流定义
     * @param variables 变量参数
     */
    public void recordInstanceCreation(WorkflowInstance instance, WorkflowDefine define, Map<String, Object> variables) {
        try {
            String businessKey = define.getBusinessKey();
            String businessId = instance.getBusinessId();

            BusinessProcessDTO processDTO = BusinessProcessDTO.createWorkflowProcess(
                    businessKey,
                    businessId,
                    "CREATE_INSTANCE",
                    "创建工作流实例",
                    instance.getId(),
                    null,
                    "开始"
            );

            businessProcessService.recordProcess(processDTO);
            log.debug("记录工作流实例创建过程: businessKey={}, businessId={}, instanceId={}",
                    businessKey, businessId, instance.getId());
        } catch (Exception e) {
            log.error("记录工作流实例创建过程失败", e);
        }
    }

    /**
     * 记录工作流任务创建过程
     *
     * @param task 工作流任务
     * @param instance 工作流实例
     * @param define 工作流定义
     * @param node 工作流节点
     */
    public void recordTaskCreation(WorkflowTask task, WorkflowInstance instance,
                                   WorkflowDefine define, WorkflowNode node) {
        try {
            String businessKey = define.getBusinessKey();
            String businessId = instance.getBusinessId();

            BusinessProcessDTO processDTO = BusinessProcessDTO.createWorkflowProcess(
                    businessKey,
                    businessId,
                    "CREATE_TASK",
                    "创建工作流任务: " + task.getName(),
                    instance.getId(),
                    task.getId(),
                    node.getName()
            );

            businessProcessService.recordProcess(processDTO);
            log.debug("记录工作流任务创建过程: businessKey={}, businessId={}, taskId={}",
                    businessKey, businessId, task.getId());
        } catch (Exception e) {
            log.error("记录工作流任务创建过程失败", e);
        }
    }

    /**
     * 记录工作流任务完成过程
     *
     * @param task 工作流任务
     * @param instance 工作流实例
     * @param define 工作流定义
     * @param node 工作流节点
     * @param variables 变量参数
     */
    public void recordTaskCompletion(WorkflowTask task, WorkflowInstance instance,
                                     WorkflowDefine define, WorkflowNode node,
                                     Map<String, Object> variables) {
        try {
            String businessKey = define.getBusinessKey();
            String businessId = instance.getBusinessId();

            // 从变量中获取操作备注
            String remark = extractRemark(variables);

            BusinessProcessDTO processDTO = BusinessProcessDTO.createWorkflowProcess(
                    businessKey,
                    businessId,
                    "COMPLETE_TASK",
                    "完成工作流任务: " + task.getName(),
                    instance.getId(),
                    task.getId(),
                    node.getName()
            );

            processDTO.setOperationRemark(remark);
            processDTO.setProcessResult(ProcessResult.SUCCESS);

            businessProcessService.recordProcess(processDTO);
            log.debug("记录工作流任务完成过程: businessKey={}, businessId={}, taskId={}",
                    businessKey, businessId, task.getId());
        } catch (Exception e) {
            log.error("记录工作流任务完成过程失败", e);
        }
    }

    /**
     * 记录工作流实例完成过程
     *
     * @param instance 工作流实例
     * @param define 工作流定义
     * @param node 结束节点
     */
    public void recordInstanceCompletion(WorkflowInstance instance, WorkflowDefine define, WorkflowNode node) {
        try {
            String businessKey = define.getBusinessKey();
            String businessId = instance.getBusinessId();

            BusinessProcessDTO processDTO = BusinessProcessDTO.createWorkflowProcess(
                    businessKey,
                    businessId,
                    "COMPLETE_INSTANCE",
                    "工作流实例完成",
                    instance.getId(),
                    null,
                    node.getName()
            );

            processDTO.setProcessResult(ProcessResult.SUCCESS);

            businessProcessService.recordProcess(processDTO);
            log.debug("记录工作流实例完成过程: businessKey={}, businessId={}, instanceId={}",
                    businessKey, businessId, instance.getId());
        } catch (Exception e) {
            log.error("记录工作流实例完成过程失败", e);
        }
    }

    /**
     * 记录工作流节点执行过程
     *
     * @param instance 工作流实例
     * @param define 工作流定义
     * @param node 工作流节点
     * @param actionType 操作类型
     * @param description 描述
     * @param variables 变量参数
     */
    public void recordNodeExecution(WorkflowInstance instance, WorkflowDefine define,
                                    WorkflowNode node, String actionType, String description,
                                    Map<String, Object> variables) {
        try {
            String businessKey = define.getBusinessKey();
            String businessId = instance.getBusinessId();

            String remark = extractRemark(variables);

            BusinessProcessDTO processDTO = BusinessProcessDTO.createWorkflowProcess(
                    businessKey,
                    businessId,
                    actionType,
                    description,
                    instance.getId(),
                    null,
                    node.getName()
            );

            processDTO.setOperationRemark(remark);
            processDTO.setProcessResult(ProcessResult.SUCCESS);

            businessProcessService.recordProcess(processDTO);
            log.debug("记录工作流节点执行过程: businessKey={}, businessId={}, nodeId={}, actionType={}",
                    businessKey, businessId, node.getId(), actionType);
        } catch (Exception e) {
            log.error("记录工作流节点执行过程失败", e);
        }
    }

    /**
     * 记录工作流异常过程
     *
     * @param instance 工作流实例
     * @param define 工作流定义
     * @param node 工作流节点
     * @param actionType 操作类型
     * @param errorMessage 错误信息
     */
    public void recordWorkflowError(WorkflowInstance instance, WorkflowDefine define,
                                    WorkflowNode node, String actionType, String errorMessage) {
        try {
            String businessKey = define.getBusinessKey();
            String businessId = instance.getBusinessId();

            BusinessProcessDTO processDTO = BusinessProcessDTO.createWorkflowProcess(
                    businessKey,
                    businessId,
                    actionType,
                    "工作流执行异常",
                    instance.getId(),
                    null,
                    node != null ? node.getName() : "未知节点"
            );

            processDTO.setOperationRemark(errorMessage);
            processDTO.setProcessResult(ProcessResult.FAILED);

            businessProcessService.recordProcess(processDTO);
            log.debug("记录工作流异常过程: businessKey={}, businessId={}, error={}",
                    businessKey, businessId, errorMessage);
        } catch (Exception e) {
            log.error("记录工作流异常过程失败", e);
        }
    }

    /**
     * 从变量中提取操作备注
     */
    private String extractRemark(Map<String, Object> variables) {
        if (variables == null) {
            return null;
        }

        // 尝试从常见的备注字段中获取值
        Object remark = variables.get("remark");
        if (remark == null) {
            remark = variables.get("comment");
        }
        if (remark == null) {
            remark = variables.get("reviewComments");
        }
        if (remark == null) {
            remark = variables.get("opinion");
        }

        return remark != null ? remark.toString() : null;
    }
}
