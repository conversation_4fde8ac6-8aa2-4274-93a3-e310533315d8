package com.gientech.workflow.strategy.impl;

import com.gientech.workflow.define.NodeType;
import com.gientech.workflow.define.WorkflowDefine;
import com.gientech.workflow.define.WorkflowNode;
import com.gientech.workflow.entity.InstanceStatus;
import com.gientech.workflow.entity.WorkflowInstance;
import com.gientech.workflow.entity.WorkflowTask;
import com.gientech.workflow.factory.WorkflowTaskFactoryManager;
import com.gientech.workflow.parser.SpringELParser;
import com.gientech.workflow.process.WorkflowProcessRecorder;
import com.gientech.workflow.strategy.NodeExecutionResult;
import com.gientech.workflow.strategy.NodeExecutionStrategy;
import com.gientech.workflow.updater.BusinessDataUpdaterManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 用户任务执行策略
 * 处理用户任务节点的执行逻辑
 *
 * <AUTHOR>
 * @date 2025年08月07日
 */
@Slf4j
@Component
public class UserTaskExecutionStrategy implements NodeExecutionStrategy {

    @Autowired
    private WorkflowTaskFactoryManager taskFactoryManager;

    @Autowired
    private SpringELParser springElParser;

    @Autowired
    private BusinessDataUpdaterManager businessDataUpdaterManager;

    @Autowired
    private WorkflowProcessRecorder processRecorder;

    @Override
    public NodeExecutionResult execute(WorkflowInstance instance, WorkflowNode node,
                                       WorkflowDefine define, Map<String, Object> variables,
                                       WorkflowTask task) {
        try {
            log.info("执行用户任务节点: 节点ID={}, 节点名称={}", node.getId(), node.getName());

            // 执行业务操作
            executeBusinessHandler(node, define, variables);

            // 创建用户任务
            WorkflowTask newTask = taskFactoryManager.createTask(instance, node, variables);
            if (newTask == null) {
                return NodeExecutionResult.failure("创建用户任务失败");
            }

            // 记录任务创建过程
            processRecorder.recordTaskCreation(newTask, instance, define, node);

            return NodeExecutionResult.success(
                    "用户任务创建成功: " + node.getName(),
                    newTask,
                    InstanceStatus.processing
            );

        } catch (Exception e) {
            log.error("执行用户任务节点失败: 节点ID={}", node.getId(), e);
            return NodeExecutionResult.failure("执行用户任务节点失败: " + e.getMessage());
        }
    }

    @Override
    public boolean supports(WorkflowNode node) {
        return NodeType.userTask.equals(node.getType());
    }

    /**
     * 执行业务处理器
     */
    private void executeBusinessHandler(WorkflowNode node, WorkflowDefine define, Map<String, Object> variables) {
        if (node.getHandler() != null) {
            try {
                // 执行SpEL表达式
                springElParser.evaluateExpression(node.getHandler(), variables);

                // 执行业务数据更新器
                businessDataUpdaterManager.beforeProcessTask(define.getBusinessKey(), variables);
            } catch (Exception e) {
                log.error("执行业务处理器失败: {}", node.getHandler(), e);
                throw new RuntimeException("执行业务处理器失败", e);
            }
        }
    }
}
