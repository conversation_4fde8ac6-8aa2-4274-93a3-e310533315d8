package com.gientech.workflow.process.migration;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.gientech.kri.input.entity.KriProcess;
import com.gientech.kri.input.mapper.KriProcessMapper;
import com.gientech.workflow.process.dto.BusinessProcessDTO;
import com.gientech.workflow.process.enums.ProcessType;
import com.gientech.workflow.process.service.IBusinessProcessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * KriProcess迁移服务
 * 负责将现有的KriProcess数据迁移到新的通用处理过程记录系统
 *
 * <AUTHOR>
 * @date 2025年08月07日
 */
@Slf4j
@Service
public class KriProcessMigrationService {

    @Autowired
    private KriProcessMapper kriProcessMapper;

    @Autowired
    private IBusinessProcessService businessProcessService;

    /**
     * 迁移所有KriProcess数据到新的处理过程记录系统
     *
     * @return 迁移的记录数量
     */
    @Transactional(rollbackFor = Exception.class)
    public int migrateAllKriProcess() {
        log.info("开始迁移KriProcess数据到新的处理过程记录系统");

        try {
            // 查询所有KriProcess记录
            List<KriProcess> kriProcessList = kriProcessMapper.selectList(new LambdaQueryWrapper<>());
            log.info("找到 {} 条KriProcess记录需要迁移", kriProcessList.size());

            if (kriProcessList.isEmpty()) {
                log.info("没有KriProcess记录需要迁移");
                return 0;
            }

            // 转换为新的处理过程记录
            List<BusinessProcessDTO> businessProcessList = new ArrayList<>();
            for (KriProcess kriProcess : kriProcessList) {
                BusinessProcessDTO businessProcess = convertKriProcessToBusinessProcess(kriProcess);
                businessProcessList.add(businessProcess);
            }

            // 批量保存到新系统
            boolean success = businessProcessService.recordProcessBatch(businessProcessList);
            if (success) {
                log.info("成功迁移 {} 条KriProcess记录", businessProcessList.size());
                return businessProcessList.size();
            } else {
                log.error("迁移KriProcess记录失败");
                throw new RuntimeException("迁移KriProcess记录失败");
            }

        } catch (Exception e) {
            log.error("迁移KriProcess数据异常", e);
            throw new RuntimeException("迁移KriProcess数据异常: " + e.getMessage(), e);
        }
    }

    /**
     * 根据业务标识迁移KriProcess数据
     *
     * @param businessKey 业务标识
     * @return 迁移的记录数量
     */
    @Transactional(rollbackFor = Exception.class)
    public int migrateKriProcessByBusinessKey(String businessKey) {
        log.info("开始迁移业务 {} 的KriProcess数据", businessKey);

        try {
            // 查询指定业务的KriProcess记录
            LambdaQueryWrapper<KriProcess> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(KriProcess::getBusinessKey, businessKey);
            List<KriProcess> kriProcessList = kriProcessMapper.selectList(queryWrapper);

            log.info("找到业务 {} 的 {} 条KriProcess记录需要迁移", businessKey, kriProcessList.size());

            if (kriProcessList.isEmpty()) {
                log.info("业务 {} 没有KriProcess记录需要迁移", businessKey);
                return 0;
            }

            // 转换为新的处理过程记录
            List<BusinessProcessDTO> businessProcessList = new ArrayList<>();
            for (KriProcess kriProcess : kriProcessList) {
                BusinessProcessDTO businessProcess = convertKriProcessToBusinessProcess(kriProcess);
                businessProcessList.add(businessProcess);
            }

            // 批量保存到新系统
            boolean success = businessProcessService.recordProcessBatch(businessProcessList);
            if (success) {
                log.info("成功迁移业务 {} 的 {} 条KriProcess记录", businessKey, businessProcessList.size());
                return businessProcessList.size();
            } else {
                log.error("迁移业务 {} 的KriProcess记录失败", businessKey);
                throw new RuntimeException("迁移业务 " + businessKey + " 的KriProcess记录失败");
            }

        } catch (Exception e) {
            log.error("迁移业务 {} 的KriProcess数据异常", businessKey, e);
            throw new RuntimeException("迁移业务 " + businessKey + " 的KriProcess数据异常: " + e.getMessage(), e);
        }
    }

    /**
     * 检查是否已经迁移过
     *
     * @param businessKey 业务标识
     * @return 是否已迁移
     */
    public boolean isAlreadyMigrated(String businessKey) {
        try {
            // 检查新系统中是否已有该业务的记录
            int count = businessProcessService.countProcess(businessKey, "");
            return count > 0;
        } catch (Exception e) {
            log.warn("检查迁移状态异常: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 验证迁移结果
     *
     * @param businessKey 业务标识
     * @return 验证结果
     */
    public MigrationValidationResult validateMigration(String businessKey) {
        try {
            // 统计原系统记录数
            LambdaQueryWrapper<KriProcess> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(KriProcess::getBusinessKey, businessKey);
            long originalCount = kriProcessMapper.selectCount(queryWrapper);

            // 统计新系统记录数（只统计从KriProcess迁移的记录）
            // 这里需要根据实际情况调整查询条件
            int newCount = businessProcessService.countProcess(businessKey, "");

            return MigrationValidationResult.builder()
                    .businessKey(businessKey)
                    .originalCount(originalCount)
                    .migratedCount(newCount)
                    .isValid(originalCount == newCount)
                    .build();

        } catch (Exception e) {
            log.error("验证迁移结果异常", e);
            return MigrationValidationResult.builder()
                    .businessKey(businessKey)
                    .isValid(false)
                    .errorMessage(e.getMessage())
                    .build();
        }
    }

    /**
     * 将KriProcess转换为BusinessProcessDTO
     */
    private BusinessProcessDTO convertKriProcessToBusinessProcess(KriProcess kriProcess) {
        return BusinessProcessDTO.builder()
                .businessKey(kriProcess.getBusinessKey())
                .businessId(kriProcess.getBusinessId())
                .processType(ProcessType.BUSINESS) // KriProcess主要记录业务操作
                .actionType(extractActionType(kriProcess.getProcessDescribe()))
                .processDescription(kriProcess.getProcessDescribe())
                .operationRemark(kriProcess.getProcessDescribe()) // 将描述作为备注
                .operatorId(kriProcess.getCreateBy())
                .operatorName(kriProcess.getCreateBy()) // 如果有用户名映射可以在这里处理
                .operationTime(kriProcess.getCreateTime())
                .build();
    }

    /**
     * 从处理描述中提取操作类型
     */
    private String extractActionType(String processDescribe) {
        if (processDescribe == null) {
            return "UNKNOWN";
        }

        // 根据描述内容推断操作类型
        String desc = processDescribe.toLowerCase();
        if (desc.contains("创建") || desc.contains("新增")) {
            return "CREATE";
        } else if (desc.contains("提交")) {
            return "SUBMIT";
        } else if (desc.contains("审核") || desc.contains("审批")) {
            return "REVIEW";
        } else if (desc.contains("反馈")) {
            return "FEEDBACK";
        } else if (desc.contains("修改") || desc.contains("更新")) {
            return "UPDATE";
        } else if (desc.contains("删除")) {
            return "DELETE";
        } else {
            return "PROCESS";
        }
    }

    /**
     * 迁移验证结果
     */
    @lombok.Data
    @lombok.Builder
    public static class MigrationValidationResult {
        private String businessKey;
        private long originalCount;
        private long migratedCount;
        private boolean isValid;
        private String errorMessage;
    }
}
