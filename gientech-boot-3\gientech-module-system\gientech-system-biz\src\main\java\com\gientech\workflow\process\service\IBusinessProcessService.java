package com.gientech.workflow.process.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gientech.workflow.process.dto.BusinessProcessDTO;
import com.gientech.workflow.process.entity.BusinessProcess;
import com.gientech.workflow.process.enums.ProcessType;

import java.util.List;
import java.util.Map;

/**
 * 通用业务处理过程记录服务接口
 *
 * <AUTHOR>
 * @date 2025年08月07日
 */
public interface IBusinessProcessService extends IService<BusinessProcess> {

    /**
     * 记录业务处理过程
     *
     * @param processDTO 处理过程数据传输对象
     * @return 是否成功
     */
    boolean recordProcess(BusinessProcessDTO processDTO);

    /**
     * 批量记录业务处理过程
     *
     * @param processList 处理过程列表
     * @return 是否成功
     */
    boolean recordProcessBatch(List<BusinessProcessDTO> processList);

    /**
     * 记录业务操作过程
     *
     * @param businessKey 业务标识
     * @param businessId 业务主键
     * @param actionType 操作类型
     * @param description 过程描述
     * @return 是否成功
     */
    boolean recordBusinessProcess(String businessKey, String businessId, String actionType, String description);

    /**
     * 记录业务操作过程（带备注）
     *
     * @param businessKey 业务标识
     * @param businessId 业务主键
     * @param actionType 操作类型
     * @param description 过程描述
     * @param remark 操作备注
     * @return 是否成功
     */
    boolean recordBusinessProcess(String businessKey, String businessId, String actionType,
                                  String description, String remark);

    /**
     * 记录业务操作过程（带扩展数据）
     *
     * @param businessKey 业务标识
     * @param businessId 业务主键
     * @param actionType 操作类型
     * @param description 过程描述
     * @param remark 操作备注
     * @param extendData 扩展数据
     * @return 是否成功
     */
    boolean recordBusinessProcess(String businessKey, String businessId, String actionType,
                                  String description, String remark, Map<String, Object> extendData);

    /**
     * 记录工作流操作过程
     *
     * @param businessKey 业务标识
     * @param businessId 业务主键
     * @param actionType 操作类型
     * @param description 过程描述
     * @param workflowInstanceId 工作流实例ID
     * @param workflowTaskId 工作流任务ID
     * @param nodeName 节点名称
     * @return 是否成功
     */
    boolean recordWorkflowProcess(String businessKey, String businessId, String actionType, String description,
                                  String workflowInstanceId, String workflowTaskId, String nodeName);

    /**
     * 记录工作流操作过程（带备注）
     *
     * @param businessKey 业务标识
     * @param businessId 业务主键
     * @param actionType 操作类型
     * @param description 过程描述
     * @param workflowInstanceId 工作流实例ID
     * @param workflowTaskId 工作流任务ID
     * @param nodeName 节点名称
     * @param remark 操作备注
     * @return 是否成功
     */
    boolean recordWorkflowProcess(String businessKey, String businessId, String actionType, String description,
                                  String workflowInstanceId, String workflowTaskId, String nodeName, String remark);

    /**
     * 批量记录业务操作过程
     *
     * @param businessKey 业务标识
     * @param businessIdList 业务主键列表
     * @param actionType 操作类型
     * @param description 过程描述
     * @return 是否成功
     */
    boolean recordBusinessProcessBatch(String businessKey, List<String> businessIdList,
                                       String actionType, String description);

    /**
     * 查询业务处理过程列表
     *
     * @param businessKey 业务标识
     * @param businessId 业务主键
     * @return 处理过程列表
     */
    List<BusinessProcess> getProcessList(String businessKey, String businessId);

    /**
     * 分页查询业务处理过程列表
     *
     * @param businessKey 业务标识
     * @param businessId 业务主键
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 分页结果
     */
    IPage<BusinessProcess> getProcessPage(String businessKey, String businessId, int pageNum, int pageSize);

    /**
     * 查询工作流处理过程列表
     *
     * @param workflowInstanceId 工作流实例ID
     * @return 处理过程列表
     */
    List<BusinessProcess> getWorkflowProcessList(String workflowInstanceId);

    /**
     * 获取业务的最新处理过程
     *
     * @param businessKey 业务标识
     * @param businessId 业务主键
     * @return 最新的处理过程记录
     */
    BusinessProcess getLatestProcess(String businessKey, String businessId);

    /**
     * 根据处理过程类型查询列表
     *
     * @param businessKey 业务标识
     * @param businessId 业务主键
     * @param processType 处理过程类型
     * @return 处理过程列表
     */
    List<BusinessProcess> getProcessListByType(String businessKey, String businessId, ProcessType processType);

    /**
     * 统计业务的处理过程数量
     *
     * @param businessKey 业务标识
     * @param businessId 业务主键
     * @return 处理过程数量
     */
    int countProcess(String businessKey, String businessId);

    /**
     * 删除业务的所有处理过程记录
     *
     * @param businessKey 业务标识
     * @param businessId 业务主键
     * @return 是否成功
     */
    boolean deleteProcessByBusiness(String businessKey, String businessId);
}
