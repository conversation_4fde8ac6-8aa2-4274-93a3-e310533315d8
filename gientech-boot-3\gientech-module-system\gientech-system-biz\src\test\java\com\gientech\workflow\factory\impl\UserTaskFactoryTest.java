package com.gientech.workflow.factory.impl;

import com.gientech.workflow.define.NodeType;
import com.gientech.workflow.define.WorkflowNode;
import com.gientech.workflow.entity.InstanceStatus;
import com.gientech.workflow.entity.TaskStatus;
import com.gientech.workflow.entity.WorkflowInstance;
import com.gientech.workflow.entity.WorkflowTask;
import com.gientech.workflow.parser.SpringELParser;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/**
 * 用户任务工厂测试
 *
 * <AUTHOR>
 * @date 2025年08月07日
 */
@ExtendWith(MockitoExtension.class)
class UserTaskFactoryTest {

    @Mock
    private SpringELParser springElParser;

    @InjectMocks
    private UserTaskFactory userTaskFactory;

    private WorkflowInstance testInstance;
    private WorkflowNode userTaskNode;
    private WorkflowNode nonUserTaskNode;
    private Map<String, Object> variables;

    @BeforeEach
    void setUp() {
        // 创建测试数据
        testInstance = WorkflowInstance.builder()
                .id("instance-1")
                .businessId("business-1")
                .status(InstanceStatus.processing)
                .build();

        userTaskNode = new WorkflowNode();
        userTaskNode.setId("user-task-1");
        userTaskNode.setName("用户审核");
        userTaskNode.setType(NodeType.userTask);
        userTaskNode.setAssignee("'admin'");
        userTaskNode.setAssigneeOrgCode("'ORG001'");

        nonUserTaskNode = new WorkflowNode();
        nonUserTaskNode.setId("end-1");
        nonUserTaskNode.setName("结束");
        nonUserTaskNode.setType(NodeType.end);

        variables = new HashMap<>();
        variables.put("businessKey", "testBusiness");
    }

    @Test
    void testCreateUserTask() {
        // 设置mock行为
        when(springElParser.evaluateExpression(eq("'admin'"), any())).thenReturn("admin");
        when(springElParser.evaluateExpression(eq("'ORG001'"), any())).thenReturn("ORG001");

        // 执行测试
        WorkflowTask result = userTaskFactory.createTask(testInstance, userTaskNode, variables);

        // 验证结果
        assertNotNull(result);
        assertEquals("instance-1", result.getWorkflowInstanceId());
        assertEquals("用户审核", result.getName());
        assertEquals(TaskStatus.created, result.getStatus());
        assertEquals("admin", result.getAssignee());
        assertEquals("ORG001", result.getAssigneeOrgCode());
        assertEquals(variables, result.getVariables());
    }

    @Test
    void testCreateTaskWithNullAssignee() {
        // 设置节点的委托人为null
        userTaskNode.setAssignee(null);
        userTaskNode.setAssigneeOrgCode("'ORG001'");

        // 设置mock行为
        when(springElParser.evaluateExpression(eq("'ORG001'"), any())).thenReturn("ORG001");

        // 执行测试
        WorkflowTask result = userTaskFactory.createTask(testInstance, userTaskNode, variables);

        // 验证结果
        assertNotNull(result);
        assertEquals("", result.getAssignee()); // 应该返回空字符串
        assertEquals("ORG001", result.getAssigneeOrgCode());
    }

    @Test
    void testCreateTaskWithExpressionError() {
        // 设置mock行为，模拟表达式解析错误
        when(springElParser.evaluateExpression(eq("'admin'"), any()))
                .thenThrow(new RuntimeException("表达式解析错误"));
        when(springElParser.evaluateExpression(eq("'ORG001'"), any())).thenReturn("ORG001");

        // 执行测试
        WorkflowTask result = userTaskFactory.createTask(testInstance, userTaskNode, variables);

        // 验证结果
        assertNotNull(result);
        assertEquals("", result.getAssignee()); // 错误时应该返回空字符串
        assertEquals("ORG001", result.getAssigneeOrgCode());
    }

    @Test
    void testCreateTaskForNonUserTaskNode() {
        // 执行测试
        WorkflowTask result = userTaskFactory.createTask(testInstance, nonUserTaskNode, variables);

        // 验证结果
        assertNull(result); // 非用户任务节点应该返回null
    }

    @Test
    void testSupportsUserTask() {
        // 执行测试
        boolean result = userTaskFactory.supports(userTaskNode);

        // 验证结果
        assertTrue(result);
    }

    @Test
    void testDoesNotSupportNonUserTask() {
        // 执行测试
        boolean result = userTaskFactory.supports(nonUserTaskNode);

        // 验证结果
        assertFalse(result);
    }

    @Test
    void testSupportsWithNullNode() {
        // 创建null类型的节点
        WorkflowNode nullTypeNode = new WorkflowNode();
        nullTypeNode.setType(null);

        // 执行测试
        boolean result = userTaskFactory.supports(nullTypeNode);

        // 验证结果
        assertFalse(result);
    }
}
