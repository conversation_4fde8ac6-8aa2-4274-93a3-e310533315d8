package com.gientech.workflow.process.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 处理过程类型枚举
 *
 * <AUTHOR>
 * @date 2025年08月07日
 */
@Getter
@AllArgsConstructor
public enum ProcessType {

    /**
     * 工作流操作
     * 通过工作流引擎处理的操作，如审核、审批等
     */
    WORKFLOW("WORKFLOW", "工作流操作"),

    /**
     * 业务操作
     * 不经过工作流引擎的业务操作，如创建、提交、反馈等
     */
    BUSINESS("BUSINESS", "业务操作"),

    /**
     * 系统操作
     * 系统自动执行的操作，如定时任务、自动处理等
     */
    SYSTEM("SYSTEM", "系统操作");

    /**
     * 类型代码
     */
    private final String code;

    /**
     * 类型描述
     */
    private final String description;

    /**
     * 根据代码获取枚举
     *
     * @param code 类型代码
     * @return 对应的枚举值
     */
    public static ProcessType fromCode(String code) {
        for (ProcessType type : ProcessType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的处理过程类型: " + code);
    }

    /**
     * 判断是否为工作流操作
     *
     * @return 是否为工作流操作
     */
    public boolean isWorkflow() {
        return this == WORKFLOW;
    }

    /**
     * 判断是否为业务操作
     *
     * @return 是否为业务操作
     */
    public boolean isBusiness() {
        return this == BUSINESS;
    }

    /**
     * 判断是否为系统操作
     *
     * @return 是否为系统操作
     */
    public boolean isSystem() {
        return this == SYSTEM;
    }
}
