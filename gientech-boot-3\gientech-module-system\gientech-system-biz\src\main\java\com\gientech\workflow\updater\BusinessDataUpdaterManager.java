package com.gientech.workflow.updater;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2025年08月07日 09:07
 */
@Slf4j
@Component
public class BusinessDataUpdaterManager {

    @Autowired
    private List<BusinessDataUpdater> businessDataUpdaters;

    @PostConstruct
    public void initializeBusinessDataUpdaters() {
        log.info("初始化业务数据更新器，共注册{}个更新器", businessDataUpdaters.size());
        for (BusinessDataUpdater businessDataUpdater : businessDataUpdaters) {
            log.info("注册业务数据更新器:{}", businessDataUpdater.getClass().getSimpleName());
        }
    }

    /**
     * 更新业务数据
     *
     * @param businessKey 业务Key
     * @param businessData 业务数据
     */
    public void beforeProcessTask(String businessKey, Map<String, Object> businessData) {
        for (BusinessDataUpdater businessDataUpdater : businessDataUpdaters) {
            if (businessKey.equals(businessDataUpdater.getBusinessKey())) {
                log.debug("使用业务数据更新器 {} 执行节点，业务Key: {}",
                        businessDataUpdater.getClass().getSimpleName(), businessKey);
                businessDataUpdater.beforeProcessTask(businessData);
                return;
            }
        }
        log.error("未找到支持业务Key:{}的业务更新器", businessKey);
    }
}
