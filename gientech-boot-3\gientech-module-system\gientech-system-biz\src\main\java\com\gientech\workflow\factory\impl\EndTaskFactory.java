package com.gientech.workflow.factory.impl;

import com.gientech.workflow.define.NodeType;
import com.gientech.workflow.define.WorkflowNode;
import com.gientech.workflow.entity.WorkflowInstance;
import com.gientech.workflow.entity.WorkflowTask;
import com.gientech.workflow.factory.WorkflowTaskFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 结束节点任务工厂
 * 结束节点不需要创建任务，只需要更新实例状态
 *
 * <AUTHOR>
 * @date 2025年08月07日
 */
@Slf4j
@Component
public class EndTaskFactory implements WorkflowTaskFactory {

    @Override
    public WorkflowTask createTask(WorkflowInstance instance, WorkflowNode node, Map<String, Object> variables) {
        if (!supports(node)) {
            return null;
        }

        log.info("到达结束节点: 节点ID={}, 节点名称={}, 实例ID={}", 
                node.getId(), node.getName(), instance.getId());

        // 结束节点不需要创建任务，返回null
        return null;
    }

    @Override
    public boolean supports(WorkflowNode node) {
        return NodeType.end.equals(node.getType());
    }
}
