package com.gientech.workflow.strategy;

import com.gientech.workflow.entity.InstanceStatus;
import com.gientech.workflow.entity.WorkflowTask;
import lombok.Builder;
import lombok.Data;

/**
 * 节点执行结果
 * 封装节点执行后的状态和结果信息
 *
 * <AUTHOR>
 * @date 2025年08月07日
 */
@Data
@Builder
public class NodeExecutionResult {

    /**
     * 执行是否成功
     */
    private boolean success;

    /**
     * 错误信息（如果执行失败）
     */
    private String errorMessage;

    /**
     * 实例状态更新
     */
    private InstanceStatus instanceStatus;

    /**
     * 创建的新任务（如果有）
     */
    private WorkflowTask newTask;

    /**
     * 是否需要继续流转到下一个节点
     */
    private boolean continueFlow;

    /**
     * 执行描述信息
     */
    private String description;

    /**
     * 创建成功结果
     */
    public static NodeExecutionResult success(String description) {
        return NodeExecutionResult.builder()
                .success(true)
                .description(description)
                .continueFlow(true)
                .build();
    }

    /**
     * 创建成功结果并指定实例状态
     */
    public static NodeExecutionResult success(String description, InstanceStatus instanceStatus) {
        return NodeExecutionResult.builder()
                .success(true)
                .description(description)
                .instanceStatus(instanceStatus)
                .continueFlow(true)
                .build();
    }

    /**
     * 创建成功结果并指定新任务
     */
    public static NodeExecutionResult success(String description, WorkflowTask newTask, InstanceStatus instanceStatus) {
        return NodeExecutionResult.builder()
                .success(true)
                .description(description)
                .newTask(newTask)
                .instanceStatus(instanceStatus)
                .continueFlow(false) // 有新任务时通常不继续流转
                .build();
    }

    /**
     * 创建失败结果
     */
    public static NodeExecutionResult failure(String errorMessage) {
        return NodeExecutionResult.builder()
                .success(false)
                .errorMessage(errorMessage)
                .continueFlow(false)
                .build();
    }
}
