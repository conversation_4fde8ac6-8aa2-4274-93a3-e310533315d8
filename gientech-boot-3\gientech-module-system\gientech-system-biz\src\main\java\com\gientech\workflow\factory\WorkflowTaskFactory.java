package com.gientech.workflow.factory;

import com.gientech.workflow.define.WorkflowNode;
import com.gientech.workflow.entity.WorkflowInstance;
import com.gientech.workflow.entity.WorkflowTask;

import java.util.Map;

/**
 * 工作流任务工厂接口
 * 用于根据不同的节点类型创建相应的工作流任务
 *
 * <AUTHOR>
 * @date 2025年08月07日
 */
public interface WorkflowTaskFactory {

    /**
     * 创建工作流任务
     *
     * @param instance  工作流实例
     * @param node      工作流节点
     * @param variables 变量参数
     * @return 创建的工作流任务，如果不需要创建任务则返回null
     */
    WorkflowTask createTask(WorkflowInstance instance, WorkflowNode node, Map<String, Object> variables);

    /**
     * 判断是否支持该节点类型
     *
     * @param node 工作流节点
     * @return 是否支持
     */
    boolean supports(WorkflowNode node);
}
