package com.gientech.workflow.process.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 处理过程结果枚举
 *
 * <AUTHOR>
 * @date 2025年08月07日
 */
@Getter
@AllArgsConstructor
public enum ProcessResult {

    /**
     * 成功
     */
    SUCCESS("SUCCESS", "成功"),

    /**
     * 失败
     */
    FAILED("FAILED", "失败"),

    /**
     * 待处理
     */
    PENDING("PENDING", "待处理"),

    /**
     * 已取消
     */
    CANCELLED("CANCELLED", "已取消"),

    /**
     * 已拒绝
     */
    REJECTED("REJECTED", "已拒绝");

    /**
     * 结果代码
     */
    private final String code;

    /**
     * 结果描述
     */
    private final String description;

    /**
     * 根据代码获取枚举
     *
     * @param code 结果代码
     * @return 对应的枚举值
     */
    public static ProcessResult fromCode(String code) {
        for (ProcessResult result : ProcessResult.values()) {
            if (result.getCode().equals(code)) {
                return result;
            }
        }
        throw new IllegalArgumentException("未知的处理过程结果: " + code);
    }

    /**
     * 判断是否为成功状态
     *
     * @return 是否为成功状态
     */
    public boolean isSuccess() {
        return this == SUCCESS;
    }

    /**
     * 判断是否为失败状态
     *
     * @return 是否为失败状态
     */
    public boolean isFailed() {
        return this == FAILED;
    }

    /**
     * 判断是否为待处理状态
     *
     * @return 是否为待处理状态
     */
    public boolean isPending() {
        return this == PENDING;
    }

    /**
     * 判断是否为终止状态（取消或拒绝）
     *
     * @return 是否为终止状态
     */
    public boolean isTerminated() {
        return this == CANCELLED || this == REJECTED;
    }
}
