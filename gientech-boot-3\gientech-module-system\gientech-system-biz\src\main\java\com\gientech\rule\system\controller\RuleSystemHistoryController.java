package com.gientech.rule.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gientech.rule.system.entity.RuleSystemHistory;
import com.gientech.rule.system.service.IRuleSystemHistoryService;
import com.gientech.rule.system.service.IRuleSystemSuggestRelateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 制度相关历史数据表
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2025-07-16
 */
@Tag(name = "制度相关历史数据表")
@RestController
@RequestMapping("/rule/system/history")
@Slf4j
public class RuleSystemHistoryController extends JeecgController<RuleSystemHistory, IRuleSystemHistoryService> {

    private IRuleSystemSuggestRelateService ruleSystemSuggestRelateService;

    @Autowired
    public void setRuleSystemSuggestRelateService(IRuleSystemSuggestRelateService ruleSystemSuggestRelateService) {
        this.ruleSystemSuggestRelateService = ruleSystemSuggestRelateService;
    }

    private IRuleSystemHistoryService ruleSystemHistoryService;

    @Autowired
    public void setRuleSystemHistoryService(IRuleSystemHistoryService ruleSystemHistoryService) {
        this.ruleSystemHistoryService = ruleSystemHistoryService;
    }

    /**
     * 分页列表查询
     *
     * @param ruleSystemHistory 实体参数
     * @param pageNo 页码
     * @param pageSize 分页大小
     * @param req 请求参数
     * @return 查询结果
     */
    @GetMapping(value = "/list")
    @Operation(summary = "分页列表查询")
    public Result<IPage<RuleSystemHistory>> queryPageList(RuleSystemHistory ruleSystemHistory,
                                                          @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                          @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                          HttpServletRequest req) {
        // 自定义查询规则
        Map<String, QueryRuleEnum> customeRuleMap = new HashMap<>();
        // 自定义多选的查询规则为：LIKE_WITH_OR
        customeRuleMap.put("dataType", QueryRuleEnum.LIKE_WITH_OR);
        customeRuleMap.put("dataCode", QueryRuleEnum.LIKE_WITH_OR);
        QueryWrapper<RuleSystemHistory> queryWrapper = QueryGenerator.initQueryWrapper(ruleSystemHistory, req.getParameterMap(), customeRuleMap);
        Page<RuleSystemHistory> page = new Page<RuleSystemHistory>(pageNo, pageSize);
        IPage<RuleSystemHistory> pageList = ruleSystemHistoryService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 根据关联主键查询历史数据
     *
     * @param relateId 关联主键
     * @return 历史数据
     */
    @GetMapping(value = "/queryByRelateId")
    @Operation(summary = "根据关联主键查询")
    public Result<IPage<RuleSystemHistory>> querySuggestListByRelateId(@RequestParam(name = "relateId", required = true) String relateId) {
        List<RuleSystemHistory> ruleSystemHistoryList = service.selectByRelateId(relateId);
        Page<RuleSystemHistory> page = new Page<>();
        page.setRecords(ruleSystemHistoryList);
        return Result.OK(page);
    }

    /**
     * 根据制度主键查询历史数据信息数量
     *
     * @param systemId 制度主键
     * @return 历史数据信息
     */
    @GetMapping(value = "/countBySystemId")
    @Operation(summary = "根据制度主键查询历史数据信息数量")
    public Result<Integer> countByRelateId(@RequestParam(name = "systemId", required = true) String systemId) {
        // 获取历史数据数量
        QueryWrapper<RuleSystemHistory> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("system_id", systemId);
        long count = service.count(queryWrapper);
        return Result.OK((int) count);
    }

    /**
     * 添加
     *
     * @param ruleSystemHistory 实体表单参数
     * @return 是否成功
     */
    @Operation(summary = "添加")
    @PostMapping(value = "/add")
    @AutoLog(value = "制度相关历史数据表-添加")
    @RequiresPermissions("rule.system:rule_system_history:add")
    public Result<String> add(@RequestBody RuleSystemHistory ruleSystemHistory) {
        ruleSystemHistoryService.save(ruleSystemHistory);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param ruleSystemHistory 实体表单参数
     * @return 是否成功
     */
    @Operation(summary = "编辑")
    @AutoLog(value = "制度相关历史数据表-编辑")
    @RequiresPermissions("rule.system:rule_system_history:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody RuleSystemHistory ruleSystemHistory) {
        ruleSystemHistoryService.updateById(ruleSystemHistory);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id 实体对象主键
     * @return 是否成功
     */
    @Operation(summary = "通过id删除")
    @DeleteMapping(value = "/delete")
    @AutoLog(value = "制度相关历史数据表-通过id删除")
    @RequiresPermissions("rule.system:rule_system_history:delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        ruleSystemHistoryService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids 多个实体对象主键
     * @return 是否成功
     */
    @Operation(summary = "批量删除")
    @DeleteMapping(value = "/deleteBatch")
    @AutoLog(value = "制度相关历史数据表-批量删除")
    @RequiresPermissions("rule.system:rule_system_history:deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.ruleSystemHistoryService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id 实体对象主键
     * @return 查询结果
     */
    @GetMapping(value = "/queryById")
    @Operation(summary = "通过id查询")
    public Result<RuleSystemHistory> queryById(@RequestParam(name = "id", required = true) String id) {
        RuleSystemHistory ruleSystemHistory = ruleSystemHistoryService.getById(id);
        if (ruleSystemHistory == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(ruleSystemHistory);
    }

    /**
     * 导出excel
     *
     * @param request 请求参数
     * @param ruleSystemHistory 实体表单参数
     */
    @RequestMapping(value = "/exportXls")
    @RequiresPermissions("rule.system:rule_system_history:exportXls")
    public ModelAndView exportXls(HttpServletRequest request, RuleSystemHistory ruleSystemHistory) {
        return super.exportXls(request, ruleSystemHistory, RuleSystemHistory.class, "制度相关历史数据表");
    }

    /**
     * 通过excel导入数据
     *
     * @param request 请求参数
     * @return 是否成功
     */
    @RequiresPermissions("rule.system:rule_system_history:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, RuleSystemHistory.class);
    }

}
