package com.gientech.workflow.strategy.impl;

import com.gientech.workflow.define.NodeType;
import com.gientech.workflow.define.WorkflowDefine;
import com.gientech.workflow.define.WorkflowNode;
import com.gientech.workflow.entity.InstanceStatus;
import com.gientech.workflow.entity.WorkflowInstance;
import com.gientech.workflow.entity.WorkflowTask;
import com.gientech.workflow.parser.SpringELParser;
import com.gientech.workflow.strategy.NodeExecutionResult;
import com.gientech.workflow.strategy.NodeExecutionStrategy;
import com.gientech.workflow.updater.BusinessDataUpdaterManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 服务任务执行策略
 * 处理服务任务节点的执行逻辑
 *
 * <AUTHOR>
 * @date 2025年08月07日
 */
@Slf4j
@Component
public class ServiceTaskExecutionStrategy implements NodeExecutionStrategy {

    @Autowired
    private SpringELParser springElParser;

    @Autowired
    private BusinessDataUpdaterManager businessDataUpdaterManager;

    @Override
    public NodeExecutionResult execute(WorkflowInstance instance, WorkflowNode node,
                                       WorkflowDefine define, Map<String, Object> variables,
                                       WorkflowTask task) {
        try {
            log.info("执行服务任务节点: 节点ID={}, 节点名称={}", node.getId(), node.getName());

            // 执行业务操作
            executeBusinessHandler(node, define, variables);

            // 服务任务自动完成，继续流转到下一个节点
            return NodeExecutionResult.success(
                    "服务任务执行完成: " + node.getName(),
                    InstanceStatus.processing
            );

        } catch (Exception e) {
            log.error("执行服务任务节点失败: 节点ID={}", node.getId(), e);
            return NodeExecutionResult.failure("执行服务任务节点失败: " + e.getMessage());
        }
    }

    @Override
    public boolean supports(WorkflowNode node) {
        return NodeType.serviceTask.equals(node.getType());
    }

    /**
     * 执行业务处理器
     */
    private void executeBusinessHandler(WorkflowNode node, WorkflowDefine define, Map<String, Object> variables) {
        if (node.getHandler() != null) {
            try {
                // 执行SpEL表达式
                springElParser.evaluateExpression(node.getHandler(), variables);

                // 执行业务数据更新器
                businessDataUpdaterManager.beforeProcessTask(define.getBusinessKey(), variables);
            } catch (Exception e) {
                log.error("执行业务处理器失败: {}", node.getHandler(), e);
                throw new RuntimeException("执行业务处理器失败", e);
            }
        }
    }
}
