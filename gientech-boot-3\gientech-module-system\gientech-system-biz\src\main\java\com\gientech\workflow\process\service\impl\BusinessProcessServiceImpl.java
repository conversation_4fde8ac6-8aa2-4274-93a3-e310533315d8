package com.gientech.workflow.process.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gientech.workflow.process.dto.BusinessProcessDTO;
import com.gientech.workflow.process.entity.BusinessProcess;
import com.gientech.workflow.process.enums.ProcessResult;
import com.gientech.workflow.process.enums.ProcessType;
import com.gientech.workflow.process.mapper.BusinessProcessMapper;
import com.gientech.workflow.process.service.IBusinessProcessService;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.vo.LoginUser;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 通用业务处理过程记录服务实现
 *
 * <AUTHOR>
 * @date 2025年08月07日
 */
@Slf4j
@Service
public class BusinessProcessServiceImpl extends ServiceImpl<BusinessProcessMapper, BusinessProcess>
        implements IBusinessProcessService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean recordProcess(BusinessProcessDTO processDTO) {
        try {
            BusinessProcess process = convertToEntity(processDTO);
            fillOperatorInfo(process);
            return save(process);
        } catch (Exception e) {
            log.error("记录业务处理过程失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean recordProcessBatch(List<BusinessProcessDTO> processList) {
        try {
            List<BusinessProcess> entityList = new ArrayList<>();
            for (BusinessProcessDTO dto : processList) {
                BusinessProcess process = convertToEntity(dto);
                fillOperatorInfo(process);
                entityList.add(process);
            }
            return saveBatch(entityList);
        } catch (Exception e) {
            log.error("批量记录业务处理过程失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean recordBusinessProcess(String businessKey, String businessId, String actionType, String description) {
        BusinessProcessDTO dto = BusinessProcessDTO.createBusinessProcess(businessKey, businessId, actionType, description);
        return recordProcess(dto);
    }

    @Override
    public boolean recordBusinessProcess(String businessKey, String businessId, String actionType,
                                         String description, String remark) {
        BusinessProcessDTO dto = BusinessProcessDTO.createBusinessProcess(businessKey, businessId, actionType, description);
        dto.setOperationRemark(remark);
        return recordProcess(dto);
    }

    @Override
    public boolean recordBusinessProcess(String businessKey, String businessId, String actionType,
                                         String description, String remark, Map<String, Object> extendData) {
        BusinessProcessDTO dto = BusinessProcessDTO.createBusinessProcess(businessKey, businessId, actionType, description);
        dto.setOperationRemark(remark);
        dto.setExtendData(extendData);
        return recordProcess(dto);
    }

    @Override
    public boolean recordWorkflowProcess(String businessKey, String businessId, String actionType, String description,
                                         String workflowInstanceId, String workflowTaskId, String nodeName) {
        BusinessProcessDTO dto = BusinessProcessDTO.createWorkflowProcess(
                businessKey, businessId, actionType, description, workflowInstanceId, workflowTaskId, nodeName);
        return recordProcess(dto);
    }

    @Override
    public boolean recordWorkflowProcess(String businessKey, String businessId, String actionType, String description,
                                         String workflowInstanceId, String workflowTaskId, String nodeName, String remark) {
        BusinessProcessDTO dto = BusinessProcessDTO.createWorkflowProcess(
                businessKey, businessId, actionType, description, workflowInstanceId, workflowTaskId, nodeName);
        dto.setOperationRemark(remark);
        return recordProcess(dto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean recordBusinessProcessBatch(String businessKey, List<String> businessIdList,
                                              String actionType, String description) {
        try {
            List<BusinessProcessDTO> processList = new ArrayList<>();
            for (String businessId : businessIdList) {
                BusinessProcessDTO dto = BusinessProcessDTO.createBusinessProcess(businessKey, businessId, actionType, description);
                processList.add(dto);
            }
            return recordProcessBatch(processList);
        } catch (Exception e) {
            log.error("批量记录业务处理过程失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public List<BusinessProcess> getProcessList(String businessKey, String businessId) {
        LambdaQueryWrapper<BusinessProcess> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusinessProcess::getBusinessKey, businessKey)
                .eq(BusinessProcess::getBusinessId, businessId)
                .orderByAsc(BusinessProcess::getSortOrder)
                .orderByAsc(BusinessProcess::getOperationTime);
        return list(queryWrapper);
    }

    @Override
    public IPage<BusinessProcess> getProcessPage(String businessKey, String businessId, int pageNum, int pageSize) {
        Page<BusinessProcess> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<BusinessProcess> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusinessProcess::getBusinessKey, businessKey)
                .eq(BusinessProcess::getBusinessId, businessId)
                .orderByAsc(BusinessProcess::getSortOrder)
                .orderByAsc(BusinessProcess::getOperationTime);
        return page(page, queryWrapper);
    }

    @Override
    public List<BusinessProcess> getWorkflowProcessList(String workflowInstanceId) {
        LambdaQueryWrapper<BusinessProcess> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusinessProcess::getWorkflowInstanceId, workflowInstanceId)
                .orderByAsc(BusinessProcess::getSortOrder)
                .orderByAsc(BusinessProcess::getOperationTime);
        return list(queryWrapper);
    }

    @Override
    public BusinessProcess getLatestProcess(String businessKey, String businessId) {
        LambdaQueryWrapper<BusinessProcess> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusinessProcess::getBusinessKey, businessKey)
                .eq(BusinessProcess::getBusinessId, businessId)
                .orderByDesc(BusinessProcess::getOperationTime)
                .last("LIMIT 1");
        return getOne(queryWrapper);
    }

    @Override
    public List<BusinessProcess> getProcessListByType(String businessKey, String businessId, ProcessType processType) {
        LambdaQueryWrapper<BusinessProcess> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusinessProcess::getBusinessKey, businessKey)
                .eq(BusinessProcess::getBusinessId, businessId)
                .eq(BusinessProcess::getProcessType, processType.getCode())
                .orderByAsc(BusinessProcess::getSortOrder)
                .orderByAsc(BusinessProcess::getOperationTime);
        return list(queryWrapper);
    }

    @Override
    public int countProcess(String businessKey, String businessId) {
        LambdaQueryWrapper<BusinessProcess> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusinessProcess::getBusinessKey, businessKey)
                .eq(BusinessProcess::getBusinessId, businessId);
        return Math.toIntExact(count(queryWrapper));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteProcessByBusiness(String businessKey, String businessId) {
        LambdaQueryWrapper<BusinessProcess> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusinessProcess::getBusinessKey, businessKey)
                .eq(BusinessProcess::getBusinessId, businessId);
        return remove(queryWrapper);
    }

    /**
     * 将DTO转换为实体
     */
    private BusinessProcess convertToEntity(BusinessProcessDTO dto) {
        return BusinessProcess.builder()
                .businessKey(dto.getBusinessKey())
                .businessId(dto.getBusinessId())
                .processType(dto.getProcessType().getCode())
                .actionType(dto.getActionType())
                .processDescription(dto.getProcessDescription())
                .processResult(dto.getProcessResult() != null ? dto.getProcessResult().getCode() : ProcessResult.SUCCESS.getCode())
                .workflowInstanceId(dto.getWorkflowInstanceId())
                .workflowTaskId(dto.getWorkflowTaskId())
                .nodeName(dto.getNodeName())
                .operationRemark(dto.getOperationRemark())
                .extendData(dto.getExtendData())
                .operatorId(dto.getOperatorId())
                .operatorName(dto.getOperatorName())
                .operatorOrgCode(dto.getOperatorOrgCode())
                .operatorOrgName(dto.getOperatorOrgName())
                .operationTime(dto.getOperationTime() != null ? dto.getOperationTime() : new Date())
                .build();
    }

    /**
     * 填充操作人信息
     */
    private void fillOperatorInfo(BusinessProcess process) {
        try {
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (loginUser != null) {
                if (process.getOperatorId() == null) {
                    process.setOperatorId(loginUser.getId());
                }
                if (process.getOperatorName() == null) {
                    process.setOperatorName(loginUser.getRealname());
                }
                if (process.getOperatorOrgCode() == null) {
                    process.setOperatorOrgCode(loginUser.getOrgCode());
                }
                if (process.getOperatorOrgName() == null) {
                    process.setOperatorOrgName(loginUser.getOrgCode());
                }
            }
        } catch (Exception e) {
            log.warn("获取当前登录用户信息失败: {}", e.getMessage());
        }
    }
}
