package com.gientech.workflow.process.adapter;

import com.gientech.kri.input.entity.KriProcess;
import com.gientech.workflow.process.entity.BusinessProcess;
import com.gientech.workflow.process.service.IBusinessProcessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * KriProcess适配器
 * 提供向后兼容的KriProcess接口，内部使用新的通用处理过程记录系统
 *
 * <AUTHOR>
 * @date 2025年08月07日
 */
@Slf4j
@Component
public class KriProcessAdapter {

    @Autowired
    private IBusinessProcessService businessProcessService;

    /**
     * 记录处理过程（兼容KriProcess接口）
     *
     * @param businessKey 业务标识
     * @param businessId 业务主键
     * @param processDescribe 过程描述
     * @return 是否成功
     */
    public boolean recordProcess(String businessKey, String businessId, String processDescribe) {
        try {
            return businessProcessService.recordBusinessProcess(businessKey, businessId, "PROCESS", processDescribe);
        } catch (Exception e) {
            log.error("记录处理过程失败: businessKey={}, businessId={}, processDescribe={}",
                    businessKey, businessId, processDescribe, e);
            return false;
        }
    }

    /**
     * 记录处理过程（带备注）
     *
     * @param businessKey 业务标识
     * @param businessId 业务主键
     * @param processDescribe 过程描述
     * @param remark 备注
     * @return 是否成功
     */
    public boolean recordProcess(String businessKey, String businessId, String processDescribe, String remark) {
        try {
            return businessProcessService.recordBusinessProcess(businessKey, businessId, "PROCESS", processDescribe, remark);
        } catch (Exception e) {
            log.error("记录处理过程失败: businessKey={}, businessId={}, processDescribe={}, remark={}",
                    businessKey, businessId, processDescribe, remark, e);
            return false;
        }
    }

    /**
     * 查询处理过程列表（兼容KriProcess接口）
     *
     * @param businessKey 业务标识
     * @param businessId 业务主键
     * @return 处理过程列表
     */
    public List<KriProcess> getProcessList(String businessKey, String businessId) {
        try {
            return businessProcessService.getProcessList(businessKey, businessId)
                    .stream()
                    .map(this::convertToKriProcess)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("查询处理过程列表失败: businessKey={}, businessId={}", businessKey, businessId, e);
            return List.of();
        }
    }

    /**
     * 获取最新处理过程（兼容KriProcess接口）
     *
     * @param businessKey 业务标识
     * @param businessId 业务主键
     * @return 最新处理过程
     */
    public KriProcess getLatestProcess(String businessKey, String businessId) {
        try {
            var latestProcess = businessProcessService.getLatestProcess(businessKey, businessId);
            return latestProcess != null ? convertToKriProcess(latestProcess) : null;
        } catch (Exception e) {
            log.error("获取最新处理过程失败: businessKey={}, businessId={}", businessKey, businessId, e);
            return null;
        }
    }

    /**
     * 统计处理过程数量（兼容KriProcess接口）
     *
     * @param businessKey 业务标识
     * @param businessId 业务主键
     * @return 处理过程数量
     */
    public int countProcess(String businessKey, String businessId) {
        try {
            return businessProcessService.countProcess(businessKey, businessId);
        } catch (Exception e) {
            log.error("统计处理过程数量失败: businessKey={}, businessId={}", businessKey, businessId, e);
            return 0;
        }
    }

    /**
     * 删除处理过程（兼容KriProcess接口）
     *
     * @param businessKey 业务标识
     * @param businessId 业务主键
     * @return 是否成功
     */
    public boolean deleteProcess(String businessKey, String businessId) {
        try {
            return businessProcessService.deleteProcessByBusiness(businessKey, businessId);
        } catch (Exception e) {
            log.error("删除处理过程失败: businessKey={}, businessId={}", businessKey, businessId, e);
            return false;
        }
    }

    /**
     * 批量记录处理过程（兼容KriProcess接口）
     *
     * @param businessKey 业务标识
     * @param businessIdList 业务主键列表
     * @param processDescribe 过程描述
     * @return 是否成功
     */
    public boolean recordProcessBatch(String businessKey, List<String> businessIdList, String processDescribe) {
        try {
            return businessProcessService.recordBusinessProcessBatch(businessKey, businessIdList, "PROCESS", processDescribe);
        } catch (Exception e) {
            log.error("批量记录处理过程失败: businessKey={}, businessIdList={}, processDescribe={}",
                    businessKey, businessIdList, processDescribe, e);
            return false;
        }
    }

    /**
     * 将BusinessProcess转换为KriProcess（向后兼容）
     */
    private KriProcess convertToKriProcess(BusinessProcess businessProcess) {
        KriProcess kriProcess = new KriProcess();
        kriProcess.setId(businessProcess.getId());
        kriProcess.setBusinessKey(businessProcess.getBusinessKey());
        kriProcess.setBusinessId(businessProcess.getBusinessId());
        kriProcess.setProcessDescribe(businessProcess.getProcessDescription());
        kriProcess.setCreateBy(businessProcess.getOperatorId());
        kriProcess.setCreateTime(businessProcess.getOperationTime());
        kriProcess.setUpdateBy(businessProcess.getUpdateBy());
        kriProcess.setUpdateTime(businessProcess.getUpdateTime());
        kriProcess.setSysOrgCode(businessProcess.getSysOrgCode());
        return kriProcess;
    }

    /**
     * 快速记录常见的业务操作
     */
    public static class QuickRecord {
        private final KriProcessAdapter adapter;

        public QuickRecord(KriProcessAdapter adapter) {
            this.adapter = adapter;
        }

        /**
         * 记录创建操作
         */
        public boolean recordCreate(String businessKey, String businessId, String entityName) {
            return adapter.recordProcess(businessKey, businessId, "创建" + entityName);
        }

        /**
         * 记录提交操作
         */
        public boolean recordSubmit(String businessKey, String businessId, String entityName) {
            return adapter.recordProcess(businessKey, businessId, "提交" + entityName);
        }

        /**
         * 记录审核操作
         */
        public boolean recordReview(String businessKey, String businessId, String entityName, String result, String opinion) {
            return adapter.recordProcess(businessKey, businessId,
                    "审核" + entityName + "：" + result, opinion);
        }

        /**
         * 记录反馈操作
         */
        public boolean recordFeedback(String businessKey, String businessId, String entityName, String feedback) {
            return adapter.recordProcess(businessKey, businessId,
                    entityName + "反馈", feedback);
        }

        /**
         * 记录修改操作
         */
        public boolean recordUpdate(String businessKey, String businessId, String entityName, String changes) {
            return adapter.recordProcess(businessKey, businessId,
                    "修改" + entityName, changes);
        }

        /**
         * 记录删除操作
         */
        public boolean recordDelete(String businessKey, String businessId, String entityName) {
            return adapter.recordProcess(businessKey, businessId, "删除" + entityName);
        }
    }

    /**
     * 获取快速记录工具
     */
    public QuickRecord quick() {
        return new QuickRecord(this);
    }
}
