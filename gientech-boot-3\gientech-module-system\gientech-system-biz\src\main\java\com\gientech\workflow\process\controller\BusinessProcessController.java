package com.gientech.workflow.process.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gientech.workflow.process.dto.BusinessProcessDTO;
import com.gientech.workflow.process.entity.BusinessProcess;
import com.gientech.workflow.process.enums.ProcessType;
import com.gientech.workflow.process.service.IBusinessProcessService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 通用业务处理过程记录控制器
 *
 * <AUTHOR>
 * @date 2025年08月07日
 */
@Tag(name = "通用业务处理过程记录")
@RestController
@RequestMapping("/common/process")
@Slf4j
public class BusinessProcessController {

    @Autowired
    private IBusinessProcessService businessProcessService;

    /**
     * 记录业务处理过程
     *
     * @param processDTO 处理过程数据传输对象
     * @return 操作结果
     */
    @AutoLog(value = "记录业务处理过程")
    @Operation(summary = "记录业务处理过程")
    @PostMapping("/record")
    public Result<Boolean> recordProcess(@RequestBody BusinessProcessDTO processDTO) {
        try {
            boolean success = businessProcessService.recordProcess(processDTO);
            if (success) {
                return Result.ok("记录处理过程成功");
            } else {
                return Result.error("记录处理过程失败");
            }
        } catch (Exception e) {
            log.error("记录业务处理过程异常: {}", e.getMessage(), e);
            return Result.error("记录处理过程异常: " + e.getMessage());
        }
    }

    /**
     * 批量记录业务处理过程
     *
     * @param processList 处理过程列表
     * @return 操作结果
     */
    @AutoLog(value = "批量记录业务处理过程")
    @Operation(summary = "批量记录业务处理过程")
    @PostMapping("/recordBatch")
    public Result<Boolean> recordProcessBatch(@RequestBody List<BusinessProcessDTO> processList) {
        try {
            boolean success = businessProcessService.recordProcessBatch(processList);
            if (success) {
                return Result.ok("批量记录处理过程成功");
            } else {
                return Result.error("批量记录处理过程失败");
            }
        } catch (Exception e) {
            log.error("批量记录业务处理过程异常: {}", e.getMessage(), e);
            return Result.error("批量记录处理过程异常: " + e.getMessage());
        }
    }

    /**
     * 快速记录业务操作过程
     *
     * @param businessKey 业务标识
     * @param businessId 业务主键
     * @param actionType 操作类型
     * @param description 过程描述
     * @param remark 操作备注
     * @return 操作结果
     */
    @AutoLog(value = "快速记录业务操作过程")
    @Operation(summary = "快速记录业务操作过程")
    @PostMapping("/recordBusiness")
    public Result<Boolean> recordBusinessProcess(
            @Parameter(description = "业务标识", required = true) @RequestParam String businessKey,
            @Parameter(description = "业务主键", required = true) @RequestParam String businessId,
            @Parameter(description = "操作类型", required = true) @RequestParam String actionType,
            @Parameter(description = "过程描述", required = true) @RequestParam String description,
            @Parameter(description = "操作备注") @RequestParam(required = false) String remark) {
        try {
            boolean success = businessProcessService.recordBusinessProcess(businessKey, businessId, actionType, description, remark);
            if (success) {
                return Result.ok("记录业务操作过程成功");
            } else {
                return Result.error("记录业务操作过程失败");
            }
        } catch (Exception e) {
            log.error("记录业务操作过程异常: {}", e.getMessage(), e);
            return Result.error("记录业务操作过程异常: " + e.getMessage());
        }
    }

    /**
     * 查询业务处理过程列表
     *
     * @param businessKey 业务标识
     * @param businessId 业务主键
     * @return 处理过程列表
     */
    @AutoLog(value = "查询业务处理过程列表")
    @Operation(summary = "查询业务处理过程列表")
    @GetMapping("/list")
    public Result<List<BusinessProcess>> getProcessList(
            @Parameter(description = "业务标识", required = true) @RequestParam String businessKey,
            @Parameter(description = "业务主键", required = true) @RequestParam String businessId) {
        try {
            List<BusinessProcess> processList = businessProcessService.getProcessList(businessKey, businessId);
            return Result.ok(processList);
        } catch (Exception e) {
            log.error("查询业务处理过程列表异常: {}", e.getMessage(), e);
            return Result.error("查询处理过程列表异常: " + e.getMessage());
        }
    }

    /**
     * 分页查询业务处理过程列表
     *
     * @param businessKey 业务标识
     * @param businessId 业务主键
     * @param pageNo 页码
     * @param pageSize 页大小
     * @return 分页结果
     */
    @AutoLog(value = "分页查询业务处理过程列表")
    @Operation(summary = "分页查询业务处理过程列表")
    @GetMapping("/page")
    public Result<IPage<BusinessProcess>> getProcessPage(
            @Parameter(description = "业务标识", required = true) @RequestParam String businessKey,
            @Parameter(description = "业务主键", required = true) @RequestParam String businessId,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") int pageNo,
            @Parameter(description = "页大小") @RequestParam(defaultValue = "10") int pageSize) {
        try {
            IPage<BusinessProcess> page = businessProcessService.getProcessPage(businessKey, businessId, pageNo, pageSize);
            return Result.ok(page);
        } catch (Exception e) {
            log.error("分页查询业务处理过程列表异常: {}", e.getMessage(), e);
            return Result.error("分页查询处理过程列表异常: " + e.getMessage());
        }
    }

    /**
     * 查询工作流处理过程列表
     *
     * @param workflowInstanceId 工作流实例ID
     * @return 处理过程列表
     */
    @AutoLog(value = "查询工作流处理过程列表")
    @Operation(summary = "查询工作流处理过程列表")
    @GetMapping("/workflow")
    public Result<List<BusinessProcess>> getWorkflowProcessList(
            @Parameter(description = "工作流实例ID", required = true) @RequestParam String workflowInstanceId) {
        try {
            List<BusinessProcess> processList = businessProcessService.getWorkflowProcessList(workflowInstanceId);
            return Result.ok(processList);
        } catch (Exception e) {
            log.error("查询工作流处理过程列表异常: {}", e.getMessage(), e);
            return Result.error("查询工作流处理过程列表异常: " + e.getMessage());
        }
    }

    /**
     * 获取业务的最新处理过程
     *
     * @param businessKey 业务标识
     * @param businessId 业务主键
     * @return 最新的处理过程记录
     */
    @AutoLog(value = "获取业务的最新处理过程")
    @Operation(summary = "获取业务的最新处理过程")
    @GetMapping("/latest")
    public Result<BusinessProcess> getLatestProcess(
            @Parameter(description = "业务标识", required = true) @RequestParam String businessKey,
            @Parameter(description = "业务主键", required = true) @RequestParam String businessId) {
        try {
            BusinessProcess process = businessProcessService.getLatestProcess(businessKey, businessId);
            return Result.ok(process);
        } catch (Exception e) {
            log.error("获取最新处理过程异常: {}", e.getMessage(), e);
            return Result.error("获取最新处理过程异常: " + e.getMessage());
        }
    }

    /**
     * 根据处理过程类型查询列表
     *
     * @param businessKey 业务标识
     * @param businessId 业务主键
     * @param processType 处理过程类型
     * @return 处理过程列表
     */
    @AutoLog(value = "根据类型查询处理过程列表")
    @Operation(summary = "根据类型查询处理过程列表")
    @GetMapping("/listByType")
    public Result<List<BusinessProcess>> getProcessListByType(
            @Parameter(description = "业务标识", required = true) @RequestParam String businessKey,
            @Parameter(description = "业务主键", required = true) @RequestParam String businessId,
            @Parameter(description = "处理过程类型", required = true) @RequestParam String processType) {
        try {
            ProcessType type = ProcessType.fromCode(processType);
            List<BusinessProcess> processList = businessProcessService.getProcessListByType(businessKey, businessId, type);
            return Result.ok(processList);
        } catch (Exception e) {
            log.error("根据类型查询处理过程列表异常: {}", e.getMessage(), e);
            return Result.error("根据类型查询处理过程列表异常: " + e.getMessage());
        }
    }

    /**
     * 统计业务的处理过程数量
     *
     * @param businessKey 业务标识
     * @param businessId 业务主键
     * @return 处理过程数量
     */
    @AutoLog(value = "统计处理过程数量")
    @Operation(summary = "统计处理过程数量")
    @GetMapping("/count")
    public Result<Integer> countProcess(
            @Parameter(description = "业务标识", required = true) @RequestParam String businessKey,
            @Parameter(description = "业务主键", required = true) @RequestParam String businessId) {
        try {
            int count = businessProcessService.countProcess(businessKey, businessId);
            return Result.ok(count);
        } catch (Exception e) {
            log.error("统计处理过程数量异常: {}", e.getMessage(), e);
            return Result.error("统计处理过程数量异常: " + e.getMessage());
        }
    }
}
