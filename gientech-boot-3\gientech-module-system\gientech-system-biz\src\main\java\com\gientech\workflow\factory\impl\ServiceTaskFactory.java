package com.gientech.workflow.factory.impl;

import com.gientech.workflow.define.NodeType;
import com.gientech.workflow.define.WorkflowNode;
import com.gientech.workflow.entity.TaskStatus;
import com.gientech.workflow.entity.WorkflowInstance;
import com.gientech.workflow.entity.WorkflowTask;
import com.gientech.workflow.factory.WorkflowTaskFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 服务任务工厂
 * 负责创建服务任务类型的工作流任务
 *
 * <AUTHOR>
 * @date 2025年08月07日
 */
@Slf4j
@Component
public class ServiceTaskFactory implements WorkflowTaskFactory {

    @Override
    public WorkflowTask createTask(WorkflowInstance instance, WorkflowNode node, Map<String, Object> variables) {
        if (!supports(node)) {
            return null;
        }

        log.info("创建服务任务: 节点ID={}, 节点名称={}", node.getId(), node.getName());

        // 服务任务通常是自动执行的，可以创建一个系统任务
        return WorkflowTask.builder()
                .workflowInstanceId(instance.getId())
                .name(node.getName())
                .status(TaskStatus.created)
                .assignee("SYSTEM") // 系统自动执行
                .assigneeOrgCode("SYSTEM")
                .variables(variables)
                .build();
    }

    @Override
    public boolean supports(WorkflowNode node) {
        return NodeType.serviceTask.equals(node.getType());
    }
}
