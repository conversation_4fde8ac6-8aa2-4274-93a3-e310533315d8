package com.gientech.workflow.factory.impl;

import com.gientech.workflow.define.NodeType;
import com.gientech.workflow.define.WorkflowNode;
import com.gientech.workflow.entity.TaskStatus;
import com.gientech.workflow.entity.WorkflowInstance;
import com.gientech.workflow.entity.WorkflowTask;
import com.gientech.workflow.factory.WorkflowTaskFactory;
import com.gientech.workflow.parser.SpringELParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 用户任务工厂
 * 负责创建用户任务类型的工作流任务
 *
 * <AUTHOR>
 * @date 2025年08月07日
 */
@Slf4j
@Component
public class UserTaskFactory implements WorkflowTaskFactory {

    @Autowired
    private SpringELParser springElParser;

    @Override
    public WorkflowTask createTask(WorkflowInstance instance, WorkflowNode node, Map<String, Object> variables) {
        if (!supports(node)) {
            return null;
        }

        log.info("创建用户任务: 节点ID={}, 节点名称={}", node.getId(), node.getName());

        return WorkflowTask.builder()
                .workflowInstanceId(instance.getId())
                .name(node.getName())
                .status(TaskStatus.created)
                .assignee(evaluateAssignee(node, variables))
                .assigneeOrgCode(evaluateAssigneeOrgCode(node, variables))
                .variables(variables)
                .build();
    }

    @Override
    public boolean supports(WorkflowNode node) {
        return NodeType.userTask.equals(node.getType());
    }

    /**
     * 解析委托人
     */
    private String evaluateAssignee(WorkflowNode node, Map<String, Object> variables) {
        if (node.getAssignee() == null) {
            log.warn("节点 {} 的委托人为空", node.getId());
            return "";
        }
        try {
            Object result = springElParser.evaluateExpression(node.getAssignee(), variables);
            return result != null ? result.toString() : "";
        } catch (Exception e) {
            log.error("解析委托人表达式失败: {}", node.getAssignee(), e);
            return "";
        }
    }

    /**
     * 解析委托人机构代码
     */
    private String evaluateAssigneeOrgCode(WorkflowNode node, Map<String, Object> variables) {
        if (node.getAssigneeOrgCode() == null) {
            log.warn("节点 {} 的委托人机构代码为空", node.getId());
            return "";
        }
        try {
            Object result = springElParser.evaluateExpression(node.getAssigneeOrgCode(), variables);
            return result != null ? result.toString() : "";
        } catch (Exception e) {
            log.error("解析委托人机构代码表达式失败: {}", node.getAssigneeOrgCode(), e);
            return "";
        }
    }
}
