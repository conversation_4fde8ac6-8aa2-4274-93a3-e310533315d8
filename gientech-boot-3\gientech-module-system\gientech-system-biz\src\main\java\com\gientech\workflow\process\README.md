# 通用业务处理过程记录系统

## 概述

通用业务处理过程记录系统是对原有KriProcess功能的重构和升级，提供了更加灵活、可扩展的处理过程记录和管理能力。系统支持工作流内外操作的统一记录，并提供了完整的前后端组件。

## 核心特性

### 1. 统一的处理过程记录
- **工作流操作**：通过工作流引擎处理的步骤（如：审核通过）
- **业务操作**：不经过工作流引擎的业务操作（如：创建、提交、反馈）
- **系统操作**：系统自动执行的操作（如：定时任务、自动处理）

### 2. 灵活的数据模型
- 支持扩展数据存储
- 完整的操作人信息记录
- 丰富的处理结果状态
- 工作流关联信息

### 3. 完整的前后端组件
- 通用的处理过程记录API
- 可视化的处理过程展示组件
- 便捷的处理过程按钮组件

### 4. 向后兼容性
- 提供KriProcess适配器
- 支持数据迁移工具
- 渐进式升级方案

## 快速开始

### 后端使用

#### 1. 基本使用

```java
@Autowired
private IBusinessProcessService businessProcessService;

// 记录业务操作
businessProcessService.recordBusinessProcess(
    "suggestion", // 业务标识
    "123456",     // 业务主键
    "CREATE",     // 操作类型
    "创建建议记录" // 过程描述
);

// 记录工作流操作
businessProcessService.recordWorkflowProcess(
    "suggestion",     // 业务标识
    "123456",         // 业务主键
    "REVIEW",         // 操作类型
    "审核通过",       // 过程描述
    "wf-inst-001",    // 工作流实例ID
    "wf-task-001",    // 工作流任务ID
    "审核节点"        // 节点名称
);

// 查询处理过程列表
List<BusinessProcess> processList = businessProcessService.getProcessList("suggestion", "123456");
```

#### 2. 使用DTO方式

```java
// 创建业务操作记录
BusinessProcessDTO dto = BusinessProcessDTO.createBusinessProcess(
    "suggestion", "123456", "SUBMIT", "提交审核"
);
dto.setOperationRemark("请尽快审核");
businessProcessService.recordProcess(dto);

// 创建工作流操作记录
BusinessProcessDTO workflowDto = BusinessProcessDTO.createWorkflowProcess(
    "suggestion", "123456", "APPROVE", "审核通过",
    "wf-inst-001", "wf-task-001", "审核节点"
);
businessProcessService.recordProcess(workflowDto);
```

#### 3. 使用工具类

```java
// 快速记录业务操作
BusinessProcessUtil.recordBusiness("suggestion", "123456", "CREATE", "创建建议记录");

// 快速记录工作流操作
BusinessProcessUtil.recordWorkflow(
    "suggestion", "123456", "REVIEW", "审核通过",
    "wf-inst-001", "wf-task-001", "审核节点", "同意通过"
);
```

### 前端使用

#### 1. 处理过程按钮组件

```vue
<template>
  <!-- 基本使用 -->
  <BusinessProcessButton
    business-key="suggestion"
    business-id="123456"
    button-text="查看处理过程"
    :show-count="true"
    :auto-load-count="true"
  />

  <!-- 自定义样式 -->
  <BusinessProcessButton
    business-key="suggestion"
    business-id="123456"
    button-type="primary"
    size="small"
    modal-title="建议处理过程"
    :show-filter="false"
  />
</template>

<script setup>
import { BusinessProcessButton } from '/@/components/BusinessProcess';
</script>
```

#### 2. 处理过程弹窗组件

```vue
<template>
  <a-button @click="showProcessModal">查看处理过程</a-button>
  
  <BusinessProcessModal
    ref="processModalRef"
    :getProcessList="getProcessList"
    :showFilter="true"
  />
</template>

<script setup>
import { ref } from 'vue';
import { BusinessProcessModal, getProcessList as apiGetProcessList } from '/@/components/BusinessProcess';

const processModalRef = ref();

const showProcessModal = () => {
  processModalRef.value?.handleOpen('suggestion', '123456', '建议处理过程');
};

const getProcessList = async (params) => {
  return await apiGetProcessList(params);
};
</script>
```

#### 3. API调用

```typescript
import { 
  recordBusinessProcess, 
  getProcessList, 
  BusinessProcessUtil 
} from '/@/components/BusinessProcess';

// 记录业务操作
await recordBusinessProcess({
  businessKey: 'suggestion',
  businessId: '123456',
  actionType: 'SUBMIT',
  description: '提交审核',
  remark: '请尽快处理'
});

// 查询处理过程
const result = await getProcessList({
  businessKey: 'suggestion',
  businessId: '123456'
});

// 使用工具类
await BusinessProcessUtil.recordBusiness(
  'suggestion', '123456', 'CREATE', '创建建议记录'
);
```

## 工作流引擎集成

系统已自动集成到工作流引擎中，会自动记录以下操作：

1. **实例创建**：创建工作流实例时自动记录
2. **任务创建**：创建工作流任务时自动记录
3. **任务完成**：完成工作流任务时自动记录
4. **实例完成**：工作流实例完成时自动记录
5. **异常处理**：工作流执行异常时自动记录

无需手动调用，工作流引擎会自动记录所有相关操作。

## 向后兼容

### 使用KriProcess适配器

```java
@Autowired
private KriProcessAdapter kriProcessAdapter;

// 兼容原有KriProcess接口
kriProcessAdapter.recordProcess("suggestion", "123456", "创建建议记录");

// 查询处理过程（返回KriProcess格式）
List<KriProcess> processList = kriProcessAdapter.getProcessList("suggestion", "123456");

// 快速记录常见操作
kriProcessAdapter.quick().recordCreate("suggestion", "123456", "建议记录");
kriProcessAdapter.quick().recordSubmit("suggestion", "123456", "建议记录");
kriProcessAdapter.quick().recordReview("suggestion", "123456", "建议记录", "通过", "同意");
```

### 数据迁移

```java
@Autowired
private KriProcessMigrationService migrationService;

// 迁移所有KriProcess数据
int migratedCount = migrationService.migrateAllKriProcess();

// 迁移指定业务的数据
int businessMigratedCount = migrationService.migrateKriProcessByBusinessKey("suggestion");

// 验证迁移结果
MigrationValidationResult result = migrationService.validateMigration("suggestion");
```

## 最佳实践

### 1. 业务操作记录

```java
// 在业务服务中记录关键操作
@Service
public class SuggestionService {
    
    @Autowired
    private IBusinessProcessService processService;
    
    public void createSuggestion(Suggestion suggestion) {
        // 业务逻辑
        suggestionMapper.insert(suggestion);
        
        // 记录处理过程
        processService.recordBusinessProcess(
            "suggestion", 
            suggestion.getId(), 
            "CREATE", 
            "创建建议记录：" + suggestion.getTitle()
        );
    }
    
    public void submitSuggestion(String suggestionId, String remark) {
        // 业务逻辑
        updateSuggestionStatus(suggestionId, "SUBMITTED");
        
        // 记录处理过程
        processService.recordBusinessProcess(
            "suggestion", 
            suggestionId, 
            "SUBMIT", 
            "提交审核", 
            remark
        );
    }
}
```

### 2. 前端集成

```vue
<template>
  <div class="suggestion-detail">
    <!-- 业务内容 -->
    <a-card title="建议详情">
      <!-- ... -->
    </a-card>
    
    <!-- 处理过程 -->
    <a-card title="处理过程" class="mt-4">
      <BusinessProcessButton
        :business-key="'suggestion'"
        :business-id="suggestionId"
        button-text="查看完整处理过程"
        button-type="link"
        :show-count="true"
        :auto-load-count="true"
      />
    </a-card>
  </div>
</template>
```

### 3. 扩展数据使用

```java
// 记录带扩展数据的处理过程
Map<String, Object> extendData = new HashMap<>();
extendData.put("originalStatus", "DRAFT");
extendData.put("newStatus", "SUBMITTED");
extendData.put("submitter", "张三");

processService.recordBusinessProcess(
    "suggestion", 
    suggestionId, 
    "SUBMIT", 
    "提交审核", 
    "请尽快处理",
    extendData
);
```

## 注意事项

1. **性能考虑**：处理过程记录是异步操作，不会影响主业务流程
2. **数据一致性**：使用事务确保处理过程记录与业务操作的一致性
3. **存储空间**：定期清理历史处理过程记录，避免数据量过大
4. **权限控制**：根据业务需要控制处理过程的查看权限

## 扩展开发

系统提供了良好的扩展性，可以根据业务需要：

1. **自定义处理过程类型**：扩展ProcessType枚举
2. **自定义处理结果**：扩展ProcessResult枚举
3. **自定义前端组件**：基于基础组件开发业务特定的展示组件
4. **自定义数据处理**：实现自定义的数据转换和处理逻辑
