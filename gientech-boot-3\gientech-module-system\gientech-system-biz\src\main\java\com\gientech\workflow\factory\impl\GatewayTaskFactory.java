package com.gientech.workflow.factory.impl;

import com.gientech.workflow.define.NodeType;
import com.gientech.workflow.define.WorkflowNode;
import com.gientech.workflow.entity.WorkflowInstance;
import com.gientech.workflow.entity.WorkflowTask;
import com.gientech.workflow.factory.WorkflowTaskFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 网关任务工厂
 * 网关节点通常不需要创建任务，只是流程控制
 *
 * <AUTHOR>
 * @date 2025年08月07日
 */
@Slf4j
@Component
public class GatewayTaskFactory implements WorkflowTaskFactory {

    @Override
    public WorkflowTask createTask(WorkflowInstance instance, WorkflowNode node, Map<String, Object> variables) {
        if (!supports(node)) {
            return null;
        }

        log.info("处理网关节点: 节点ID={}, 节点名称={}", node.getId(), node.getName());

        // 网关节点不需要创建任务，返回null
        return null;
    }

    @Override
    public boolean supports(WorkflowNode node) {
        return NodeType.gateway.equals(node.getType());
    }
}
