package com.gientech.workflow.util;

import com.gientech.workflow.define.WorkflowNode;
import com.gientech.workflow.entity.TaskStatus;
import com.gientech.workflow.entity.WorkflowInstance;
import com.gientech.workflow.entity.WorkflowTask;
import com.gientech.workflow.parser.SpringELParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 工作流向后兼容工具类
 * 提供与旧版本API兼容的方法
 *
 * <AUTHOR>
 * @date 2025年08月07日
 */
@Slf4j
@Component
public class WorkflowCompatibilityUtil {

    @Autowired
    private SpringELParser springElParser;

    /**
     * 构建用户任务（向后兼容方法）
     * 保持与原有buildTask方法相同的行为
     *
     * @param instance  流程实例
     * @param node      节点
     * @param variables 参数
     * @return 工作流任务
     */
    public WorkflowTask buildTask(WorkflowInstance instance, WorkflowNode node, Map<String, Object> variables) {
        log.warn("使用向后兼容的buildTask方法，建议迁移到新的工厂模式");
        
        return WorkflowTask.builder()
                .workflowInstanceId(instance.getId())
                .name(node.getName())
                .status(TaskStatus.created)
                .assignee(evaluateExpression(node.getAssignee(), variables))
                .assigneeOrgCode(evaluateExpression(node.getAssigneeOrgCode(), variables))
                .variables(variables)
                .build();
    }

    /**
     * 安全地执行SpEL表达式
     */
    private String evaluateExpression(String expression, Map<String, Object> variables) {
        if (expression == null) {
            return "";
        }
        try {
            Object result = springElParser.evaluateExpression(expression, variables);
            return result != null ? result.toString() : "";
        } catch (Exception e) {
            log.error("执行表达式失败: {}", expression, e);
            return "";
        }
    }

    /**
     * 检查节点类型是否需要创建任务（向后兼容）
     */
    public boolean shouldCreateTask(WorkflowNode node) {
        if (node == null || node.getType() == null) {
            return false;
        }
        
        switch (node.getType()) {
            case userTask:
            case serviceTask:
                return true;
            case start:
            case end:
            case gateway:
            default:
                return false;
        }
    }

    /**
     * 获取节点类型的描述（向后兼容）
     */
    public String getNodeTypeDescription(WorkflowNode node) {
        if (node == null || node.getType() == null) {
            return "未知节点类型";
        }
        
        return switch (node.getType()) {
            case start -> "开始节点";
            case userTask -> "用户任务";
            case serviceTask -> "服务任务";
            case gateway -> "网关节点";
            case end -> "结束节点";
        };
    }
}
