package com.gientech.workflow.factory;

import com.gientech.workflow.define.WorkflowNode;
import com.gientech.workflow.entity.WorkflowInstance;
import com.gientech.workflow.entity.WorkflowTask;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 工作流任务工厂管理器
 * 负责管理所有的任务工厂，并根据节点类型选择合适的工厂
 *
 * <AUTHOR>
 * @date 2025年08月07日
 */
@Slf4j
@Component
public class WorkflowTaskFactoryManager {

    @Autowired
    private List<WorkflowTaskFactory> taskFactories;

    @PostConstruct
    public void init() {
        log.info("初始化工作流任务工厂管理器，共注册 {} 个工厂", taskFactories.size());
        for (WorkflowTaskFactory factory : taskFactories) {
            log.info("注册任务工厂: {}", factory.getClass().getSimpleName());
        }
    }

    /**
     * 创建工作流任务
     *
     * @param instance 工作流实例
     * @param node 工作流节点
     * @param variables 变量参数
     * @return 创建的工作流任务，如果不需要创建任务则返回null
     */
    public WorkflowTask createTask(WorkflowInstance instance, WorkflowNode node, Map<String, Object> variables) {
        for (WorkflowTaskFactory factory : taskFactories) {
            if (factory.supports(node)) {
                log.debug("使用工厂 {} 创建任务，节点类型: {}",
                        factory.getClass().getSimpleName(), node.getType());
                return factory.createTask(instance, node, variables);
            }
        }

        log.warn("未找到支持节点类型 {} 的任务工厂", node.getType());
        return null;
    }

    /**
     * 检查是否有工厂支持该节点类型
     *
     * @param node 工作流节点
     * @return 是否支持
     */
    public boolean isSupported(WorkflowNode node) {
        return taskFactories.stream().anyMatch(factory -> factory.supports(node));
    }
}
