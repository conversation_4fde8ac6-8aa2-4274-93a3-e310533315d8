package com.gientech.workflow.factory;

import com.gientech.workflow.define.NodeType;
import com.gientech.workflow.define.WorkflowNode;
import com.gientech.workflow.entity.InstanceStatus;
import com.gientech.workflow.entity.TaskStatus;
import com.gientech.workflow.entity.WorkflowInstance;
import com.gientech.workflow.entity.WorkflowTask;
import com.gientech.workflow.factory.impl.EndTaskFactory;
import com.gientech.workflow.factory.impl.UserTaskFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * 工作流任务工厂管理器测试
 *
 * <AUTHOR>
 * @date 2025年08月07日
 */
@ExtendWith(MockitoExtension.class)
class WorkflowTaskFactoryManagerTest {

    @Mock
    private UserTaskFactory userTaskFactory;

    @Mock
    private EndTaskFactory endTaskFactory;

    @InjectMocks
    private WorkflowTaskFactoryManager factoryManager;

    private WorkflowInstance testInstance;
    private WorkflowNode userTaskNode;
    private WorkflowNode endNode;
    private Map<String, Object> variables;

    @BeforeEach
    void setUp() {
        // 通过反射设置工厂列表，因为实际使用@Autowired注入
        try {
            java.lang.reflect.Field field = WorkflowTaskFactoryManager.class.getDeclaredField("taskFactories");
            field.setAccessible(true);
            field.set(factoryManager, Arrays.asList(userTaskFactory, endTaskFactory));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        // 创建测试数据
        testInstance = WorkflowInstance.builder()
                .id("instance-1")
                .businessId("business-1")
                .status(InstanceStatus.processing)
                .build();

        userTaskNode = new WorkflowNode();
        userTaskNode.setId("user-task-1");
        userTaskNode.setName("用户审核");
        userTaskNode.setType(NodeType.userTask);
        userTaskNode.setAssignee("'admin'");
        userTaskNode.setAssigneeOrgCode("'ORG001'");

        endNode = new WorkflowNode();
        endNode.setId("end-1");
        endNode.setName("结束");
        endNode.setType(NodeType.end);

        variables = new HashMap<>();
        variables.put("businessKey", "testBusiness");
    }

    @Test
    void testCreateUserTask() {
        // 准备测试数据
        WorkflowTask expectedTask = WorkflowTask.builder()
                .workflowInstanceId("instance-1")
                .name("用户审核")
                .status(TaskStatus.created)
                .assignee("admin")
                .assigneeOrgCode("ORG001")
                .variables(variables)
                .build();

        // 设置mock行为
        when(userTaskFactory.supports(userTaskNode)).thenReturn(true);
        when(userTaskFactory.createTask(testInstance, userTaskNode, variables)).thenReturn(expectedTask);
        when(endTaskFactory.supports(userTaskNode)).thenReturn(false);

        // 执行测试
        WorkflowTask result = factoryManager.createTask(testInstance, userTaskNode, variables);

        // 验证结果
        assertNotNull(result);
        assertEquals("instance-1", result.getWorkflowInstanceId());
        assertEquals("用户审核", result.getName());
        assertEquals(TaskStatus.created, result.getStatus());
        assertEquals("admin", result.getAssignee());
        assertEquals("ORG001", result.getAssigneeOrgCode());
    }

    @Test
    void testCreateEndTask() {
        // 设置mock行为
        when(userTaskFactory.supports(endNode)).thenReturn(false);
        when(endTaskFactory.supports(endNode)).thenReturn(true);
        when(endTaskFactory.createTask(testInstance, endNode, variables)).thenReturn(null);

        // 执行测试
        WorkflowTask result = factoryManager.createTask(testInstance, endNode, variables);

        // 验证结果
        assertNull(result); // 结束节点不创建任务
    }

    @Test
    void testUnsupportedNodeType() {
        WorkflowNode unsupportedNode = new WorkflowNode();
        unsupportedNode.setId("unknown-1");
        unsupportedNode.setType(NodeType.gateway);

        // 设置mock行为
        when(userTaskFactory.supports(unsupportedNode)).thenReturn(false);
        when(endTaskFactory.supports(unsupportedNode)).thenReturn(false);

        // 执行测试
        WorkflowTask result = factoryManager.createTask(testInstance, unsupportedNode, variables);

        // 验证结果
        assertNull(result);
    }

    @Test
    void testIsSupported() {
        // 设置mock行为
        when(userTaskFactory.supports(userTaskNode)).thenReturn(true);

        // 执行测试
        boolean result = factoryManager.isSupported(userTaskNode);

        // 验证结果
        assertTrue(result);
    }

    @Test
    void testIsNotSupported() {
        WorkflowNode unsupportedNode = new WorkflowNode();
        unsupportedNode.setType(NodeType.gateway);

        // 设置mock行为
        when(userTaskFactory.supports(unsupportedNode)).thenReturn(false);
        when(endTaskFactory.supports(unsupportedNode)).thenReturn(false);

        // 执行测试
        boolean result = factoryManager.isSupported(unsupportedNode);

        // 验证结果
        assertFalse(result);
    }
}
